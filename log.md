# 修改日志

## 2025-08-24

### 修复代理等级字段切换时选项为空的问题

**文件**: `apps/web-antd/src/views/argame/user/data.ts`

**问题描述**: 
- 当身份类型切换到代理（idType === '1'）时，代理等级字段的选项为空
- 原因是使用了静态的空数组 `options: []`，没有动态加载数据

**修改内容**:
将代理等级字段从普通 Select 改为 ApiSelect，动态加载代理等级选项

**修改前**:
```typescript
{
  label: '代理等级',
  fieldName: 'levelId',
  component: 'Select',
  rules: 'selectRequired',
  componentProps: {
    options: [], // 静态空数组
  },
  dependencies: {
    show: (values) => {
      const idType = String(values.idType);
      return idType === '1';
    },
    triggerFields: ['idType'],
  },
},
```

**修改后**:
```typescript
{
  label: '代理等级',
  fieldName: 'levelId',
  component: 'ApiSelect',
  rules: 'selectRequired',
  componentProps: {
    api: async () => {
      const { levelConfigList } = await import('#/api/argame/levelConfig');
      const result = await levelConfigList({
        pageNum: 1,
        pageSize: 1000,
        idType: '1', // 代理类型
      });
      return result.rows.map((item) => ({
        label: item.levelName,
        value: item.levelId,
      }));
    },
    immediate: false, // 不立即加载
  },
  dependencies: {
    show: (values) => {
      const idType = String(values.idType);
      return idType === '1';
    },
    triggerFields: ['idType'],
  },
},
```

**预期效果**:
- 当身份类型切换到代理时，代理等级字段会动态加载并显示可用的等级选项
- 使用 `immediate: false` 避免不必要的初始加载
- 只有在字段显示时才会触发 API 调用

**技术优势**:
- ✅ 动态加载数据，确保选项始终是最新的
- ✅ 按需加载，只有在需要时才请求数据
- ✅ 使用 ApiSelect 组件，支持加载状态显示
- ✅ 异步导入 API，优化初始包大小
