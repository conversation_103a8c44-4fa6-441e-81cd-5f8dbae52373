# 修改日志

## 2025-08-24

### 修复 Vite 打包时 ant-design-vue 组件导入冲突问题

**文件**: `playground/src/adapter/component/index.ts`

**问题描述**: 
- 打包时报错：Button 组件被动态导入和静态导入，导致模块冲突
- 错误信息：`is dynamically imported by ... but also statically imported by ...`
- 原因是项目中同时存在动态导入和静态导入 ant-design-vue 组件

**修改内容**:
将所有 ant-design-vue 组件从动态导入改为统一的静态导入

**修改前**:
```typescript
const Button = defineAsyncComponent(() => import('ant-design-vue/es/button'));
const Checkbox = defineAsyncComponent(() => import('ant-design-vue/es/checkbox'));
// ... 其他动态导入
```

**修改后**:
```typescript
import {
  AutoComplete,
  Button,
  Checkbox,
  DatePicker,
  Divider,
  Input,
  InputNumber,
  Mentions,
  notification,
  Radio,
  Rate,
  Select,
  Space,
  Switch,
  TimePicker,
  TreeSelect,
  Upload,
} from 'ant-design-vue';

// 从组件中提取子组件
const CheckboxGroup = Checkbox.Group;
const InputPassword = Input.Password;
const RadioGroup = Radio.Group;
const RangePicker = DatePicker.RangePicker;
const Textarea = Input.TextArea;
```

**预期效果**:
- 解决 Vite 打包时的模块冲突问题
- 统一组件导入方式，避免动态导入和静态导入混用
- 打包过程不再报错，成功完成构建

**技术说明**:
- 动态导入 (`defineAsyncComponent`) 和静态导入不能混用同一个模块
- Vite 在打包时会检测到这种冲突并报错
- 统一使用静态导入可以避免这个问题，同时保持代码的一致性

### 优化组件导入方式以降低内存和 CPU 使用率

**文件**: `playground/src/adapter/component/index.ts`

**问题描述**:
- 静态导入所有组件会导致初始包体积过大
- 可能造成 CPU 和内存使用率过高
- 需要在解决冲突和性能之间找到平衡

**优化方案**:
使用 `lib` 路径替代 `es` 路径进行动态导入，避免与项目中的静态导入冲突

**修改内容**:
```typescript
// 修改前（会导致冲突）
const Button = defineAsyncComponent(() => import('ant-design-vue/es/button'));

// 修改后（避免冲突且保持按需加载）
const Button = defineAsyncComponent(() => import('ant-design-vue/lib/button'));
```

**优势**:
- ✅ 保持按需加载，减少初始包体积
- ✅ 避免与静态导入的 `es` 模块冲突
- ✅ 降低内存和 CPU 使用率
- ✅ 组件仍然异步加载，提高首屏性能

**技术原理**:
- `ant-design-vue/es/` 和 `ant-design-vue/lib/` 是同一组件的不同构建版本
- 使用不同路径可以避免 Vite 检测到的模块冲突
- 保持异步加载特性，只在需要时加载组件
