# 修改日志

## 2025-08-13

### 修复用户列表时间显示问题

**文件**: `apps/web-antd/src/views/argame/user/data.ts`

**问题描述**: 
- 用户列表中"注册时间/最后登录时间/最后登录IP"列只显示最后登录时间，且显示为 "1970-01-01 08:00:00"
- 注册时间和最后登录IP没有正确显示

**修改内容**:
1. 修复注册时间显示：添加了时间格式化处理 `dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')`
2. 修复最后登录时间显示：添加了数值验证 `Number(row.lastLoginTime) > 0` 来避免显示 1970-01-01 的问题
3. 保持最后登录IP的原有逻辑不变

**修改前**:
```typescript
`注册时间: ${row?.createTime || '--'}`,
`最后登录时间: ${dayjs(Number(row?.lastLoginTime)).format('YYYY-MM-DD HH:mm:ss') || ''}`,
```

**修改后**:
```typescript
`注册时间: ${row?.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '--'}`,
`最后登录时间: ${row?.lastLoginTime && Number(row.lastLoginTime) > 0 ? dayjs(Number(row.lastLoginTime)).format('YYYY-MM-DD HH:mm:ss') : '--'}`,
```

**预期效果**:
- 注册时间将正确显示格式化后的时间
- 最后登录时间只有在有效时才显示，否则显示 "--"
- 最后登录IP保持原有显示逻辑

### 修复区号字段禁用逻辑

**文件**: `apps/web-antd/src/views/argame/user/data.ts`

**问题描述**:
- 区号字段的禁用逻辑不正确，当前是在 userId 为空时禁用
- 需求是当 userId 不为空时禁用区号字段

**修改内容**:
修改区号字段的 dependencies.disabled 逻辑

**修改前**:
```typescript
disabled: (values) => {
  // 在新增模式下（没有adminId）或者roleType=3时显示
  console.log(values.userId);
  return !values.userId;
},
```

**修改后**:
```typescript
disabled: (values) => {
  // 当userId不为空时禁用区号字段
  console.log(values.userId);
  return !!values.userId;
},
```

**预期效果**:
- 当 userId 有值时，区号字段将被禁用
- 当 userId 为空时，区号字段可以正常编辑

### 修复 subUserModalApi.open 错误并传递父级用户ID

**文件**: `apps/web-antd/src/views/argame/user/index.vue`

**问题描述**:
1. 点击"查看下级"按钮时报错：`subUserModalApi.open is not a function`
2. 需要将当前行的 userId 传递给子用户模态框作为查询参数

**修改内容**:
1. 修复 `useVbenModal` 的解构赋值，正确获取 SubUserModal 组件
2. 在模板中添加 SubUserModal 组件以确保正确初始化
3. 修改 `handleImport` 函数接收行数据并传递给模态框
4. 更新按钮点击事件传递当前行数据

**修改前**:
```typescript
const [, subUserModalApi] = useVbenModal({
  connectedComponent: subUserModal,
});

function handleImport() {
  subUserModalApi.open();
}

@click="handleImport"
```

**修改后**:
```typescript
const [SubUserModal, subUserModalApi] = useVbenModal({
  connectedComponent: subUserModal,
});

function handleImport(row: Required<UserForm>) {
  subUserModalApi.setData({ parentUserId: row.userId });
  subUserModalApi.open();
}

@click="handleImport(row)"
```

**模板修改**:
```vue
<UserModal @reload="tableApi.query()" />
<SubUserModal @reload="tableApi.query()" />
```

**预期效果**:
- 点击"查看下级"按钮时不再报错
- 当前行的 userId 会作为 parentUserId 传递给子用户模态框
- 子用户模态框可以使用这个参数进行后端查询

### 重构下级用户模态框以正确使用传入的parentUserId

**文件**: `apps/web-antd/src/views/argame/user/modal/sub-user-modal.vue`

**问题描述**:
- 原有的 sub-user-modal.vue 是代码生成工具的模态框，不是用于显示下级用户
- 没有使用传入的 parentUserId 参数进行查询

**修改内容**:
1. 完全重构模态框，移除代码生成相关逻辑
2. 添加 parentUserId 响应式变量来存储传入的父级用户ID
3. 修改 proxyConfig.ajax.query 使用 userList API 并传入 parentId 参数
4. 在 onOpenChange 中获取传入的数据并设置 parentUserId
5. 移除确认按钮，设置为只读模态框

**主要修改**:
```typescript
// 添加父级用户ID变量
const parentUserId = ref<string>('');

// 修改查询逻辑
proxyConfig: {
  ajax: {
    query: async ({ page }, formValues = {}) => {
      return await userList({
        pageNum: page.currentPage,
        pageSize: page.pageSize,
        parentId: parentUserId.value,  // 使用父级用户ID查询
        ...formValues,
      });
    },
  },
},

// 在模态框打开时获取传入的数据
onOpenChange: async (isOpen) => {
  if (!isOpen) return null;
  const data = modalApi.getData();
  if (data?.parentUserId) {
    parentUserId.value = data.parentUserId;
    await tableApi.query();
  }
},
```

**预期效果**:
- 模态框打开时会自动使用传入的 parentUserId 查询下级用户
- 显示指定用户的所有下级用户列表
- 模态框标题改为"下级用户列表"，移除确认按钮

### 修复表格查询时机问题

**文件**: `apps/web-antd/src/views/argame/user/modal/sub-user-modal.vue`

**问题描述**:
- 在 onOpenChange 中直接调用 tableApi.query() 时报错：`this.grid.commitProxy is not a function`
- 原因是模态框打开时表格还没有完全初始化

**修改内容**:
1. 添加 watch 监听 parentUserId 变化
2. 简化 onOpenChange 逻辑，只设置 parentUserId 值
3. 在 watch 中延迟执行查询，确保表格已初始化

**修改前**:
```typescript
onOpenChange: async (isOpen) => {
  if (!isOpen) return null;
  const data = modalApi.getData();
  if (data?.parentUserId) {
    parentUserId.value = data.parentUserId;
    await tableApi.query(); // 这里会报错
  }
},
```

**修改后**:
```typescript
onOpenChange: async (isOpen) => {
  if (!isOpen) return null;
  const data = modalApi.getData();
  if (data?.parentUserId) {
    parentUserId.value = data.parentUserId;
  }
},

// 监听 parentUserId 变化，当有值时触发查询
watch(parentUserId, (newValue) => {
  if (newValue) {
    setTimeout(() => {
      tableApi.query();
    }, 200);
  }
});
```

**预期效果**:
- 解决表格查询时机问题，不再报错
- 模态框打开后会正确查询并显示下级用户列表

### 修复 parentId 参数未传递问题

**文件**: `apps/web-antd/src/views/argame/user/modal/sub-user-modal.vue`

**问题描述**:
- 虽然 parentUserId 已经正确设置，但接口调用时没有带上 parentId 参数
- 调试显示 parentUserId 变化为正确的值，但查询参数中缺少 parentId

**修改内容**:
1. 修改查询逻辑，确保每次查询时都能获取到最新的 parentUserId 值
2. 添加条件判断，只有当 parentUserId 有值时才添加 parentId 参数
3. 添加详细的调试日志

**修改前**:
```typescript
query: async ({ page }, formValues = {}) => {
  return await userList({
    pageNum: page.currentPage,
    pageSize: page.pageSize,
    parentId: parentUserId.value,
    ...formValues,
  });
},
```

**修改后**:
```typescript
query: async ({ page }, formValues = {}) => {
  const queryParams = {
    pageNum: page.currentPage,
    pageSize: page.pageSize,
    ...formValues,
  };

  // 只有当 parentUserId 有值时才添加 parentId 参数
  if (parentUserId.value) {
    queryParams.parentId = parentUserId.value;
  }

  console.log('查询下级用户参数:', queryParams);
  return await userList(queryParams);
},
```

**预期效果**:
- 接口调用时会正确带上 parentId 参数
- 可以正确查询指定用户的下级用户列表

### 隐藏下级用户列表中的操作列

**文件**: `apps/web-antd/src/views/argame/user/modal/sub-user-modal.vue`

**问题描述**:
- 下级用户列表模态框中显示了操作列，但这是一个查看页面，不需要操作功能

**修改内容**:
1. 创建过滤后的列配置，移除操作列
2. 在 gridOptions 中使用过滤后的列配置

**修改内容**:
```typescript
// 过滤掉操作列，用于下级用户查看页面
const subUserColumns = columns.filter(col => col.field !== 'action');

const gridOptions: VxeGridProps = {
  // ...其他配置
  columns: subUserColumns, // 使用过滤后的列配置
  // ...
};
```

**预期效果**:
- 下级用户列表模态框中不再显示操作列
- 保持其他所有列的正常显示和功能

### 过滤下级用户查询表单中的上级用户ID字段

**文件**: `apps/web-antd/src/views/argame/user/modal/sub-user-modal.vue`

**问题描述**:
- 下级用户列表的查询表单中包含"上级用户ID"字段，但在查看下级用户时父级ID已确定，不需要此字段

**修改内容**:
1. 创建过滤后的查询表单配置，移除上级用户ID字段
2. 在 formOptions 中使用过滤后的查询配置

**修改内容**:
```typescript
// 过滤掉上级用户ID查询字段，用于下级用户查看页面
const subUserQuerySchema = querySchema().filter(field => field.fieldName !== 'parentId');

const formOptions: VbenFormProps = {
  schema: subUserQuerySchema, // 使用过滤后的查询配置
  // ...其他配置
};
```

**预期效果**:
- 下级用户列表的查询表单中不再显示"上级用户ID"字段
- 其他查询条件保持正常功能
- 查询逻辑中仍会自动带上正确的 parentId 参数

### 优化下级用户模态框按钮配置

**文件**: `apps/web-antd/src/views/argame/user/modal/sub-user-modal.vue`

**问题描述**:
- 下级用户列表模态框显示确认和取消按钮，但这是查看页面不需要确认操作
- 取消按钮的文字应该改为"关闭"更符合语义

**修改内容**:
1. 保持 `:show-ok="false"` 隐藏确认按钮
2. 添加 `cancel-text="关闭"` 将取消按钮文字改为"关闭"

**修改前**:
```vue
<BasicModal class="w-[1400px]" title="下级用户列表">
```

**修改后**:
```vue
<BasicModal
  class="w-[1400px]"
  title="下级用户列表"
  :show-confirm-button="false"
  cancel-text="关闭"
>
```

**说明**:
- 移除了错误的 `modalProps` 配置（该属性不存在）
- 在模板中直接设置 `:show-confirm-button="false"` 隐藏确认按钮
- 设置 `cancel-text="关闭"` 修改按钮文字

### 修复用户类型数组渲染问题

**文件**: `apps/web-antd/src/views/argame/markCategory/data.ts`

**问题描述**:
- 用户类型字段 `userType` 可能是数组格式 `["1", "2", "3", "4"]`
- 原有渲染逻辑只处理单个值，无法正确显示数组类型的数据

**修改内容**:
修改用户类型字段的渲染逻辑，支持数组和单个值两种数据格式

**修改前**:
```typescript
slots: {
  default: ({ row }) => {
    return renderDict(row.userType, 'argame_user_type');
  },
},
```

**修改后**:
```typescript
slots: {
  default: ({ row }) => {
    // 处理数组类型的用户类型
    if (Array.isArray(row.userType)) {
      return row.userType
        .map(type => renderDict(type, 'argame_user_type'))
        .join(', ');
    }
    // 处理单个值
    return renderDict(row.userType, 'argame_user_type');
  },
},
```

**预期效果**:
- 当 userType 为数组 `["1", "2", "3", "4"]` 时，会渲染为对应的字典值并用逗号分隔
- 当 userType 为单个值时，正常渲染字典值
- 兼容两种数据格式，提高代码健壮性

**预期效果**:
- 模态框只显示一个"关闭"按钮
- 按钮文字更符合查看页面的语义
- 用户体验更加直观

### 修复 subUserModalApi.open 错误

**文件**: `apps/web-antd/src/views/argame/user/index.vue`

**问题描述**:
- 点击"查看下级"按钮时报错：`subUserModalApi.open is not a function`
- 原因是 `useVbenModal` 的解构赋值不正确，跳过了 Modal 组件导致 API 未正确初始化

**修改内容**:
1. 修复 `useVbenModal` 的解构赋值，正确获取 SubUserModal 组件
2. 在模板中添加 SubUserModal 组件以确保正确初始化

**修改前**:
```typescript
const [, subUserModalApi] = useVbenModal({
  connectedComponent: subUserModal,
});
```

**修改后**:
```typescript
const [SubUserModal, subUserModalApi] = useVbenModal({
  connectedComponent: subUserModal,
});
```

**模板修改**:
```vue
<UserModal @reload="tableApi.query()" />
<SubUserModal @reload="tableApi.query()" />
```

**预期效果**:
- 点击"查看下级"按钮时不再报错
- subUserModalApi.open() 方法可以正常调用
- 子用户模态框可以正常打开
