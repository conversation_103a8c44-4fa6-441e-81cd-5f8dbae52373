# 修改日志

## 2025-08-13

### 修复用户列表时间显示问题

**文件**: `apps/web-antd/src/views/argame/user/data.ts`

**问题描述**: 
- 用户列表中"注册时间/最后登录时间/最后登录IP"列只显示最后登录时间，且显示为 "1970-01-01 08:00:00"
- 注册时间和最后登录IP没有正确显示

**修改内容**:
1. 修复注册时间显示：添加了时间格式化处理 `dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')`
2. 修复最后登录时间显示：添加了数值验证 `Number(row.lastLoginTime) > 0` 来避免显示 1970-01-01 的问题
3. 保持最后登录IP的原有逻辑不变

**修改前**:
```typescript
`注册时间: ${row?.createTime || '--'}`,
`最后登录时间: ${dayjs(Number(row?.lastLoginTime)).format('YYYY-MM-DD HH:mm:ss') || ''}`,
```

**修改后**:
```typescript
`注册时间: ${row?.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '--'}`,
`最后登录时间: ${row?.lastLoginTime && Number(row.lastLoginTime) > 0 ? dayjs(Number(row.lastLoginTime)).format('YYYY-MM-DD HH:mm:ss') : '--'}`,
```

**预期效果**:
- 注册时间将正确显示格式化后的时间
- 最后登录时间只有在有效时才显示，否则显示 "--"
- 最后登录IP保持原有显示逻辑

### 修复区号字段禁用逻辑

**文件**: `apps/web-antd/src/views/argame/user/data.ts`

**问题描述**:
- 区号字段的禁用逻辑不正确，当前是在 userId 为空时禁用
- 需求是当 userId 不为空时禁用区号字段

**修改内容**:
修改区号字段的 dependencies.disabled 逻辑

**修改前**:
```typescript
disabled: (values) => {
  // 在新增模式下（没有adminId）或者roleType=3时显示
  console.log(values.userId);
  return !values.userId;
},
```

**修改后**:
```typescript
disabled: (values) => {
  // 当userId不为空时禁用区号字段
  console.log(values.userId);
  return !!values.userId;
},
```

**预期效果**:
- 当 userId 有值时，区号字段将被禁用
- 当 userId 为空时，区号字段可以正常编辑

### 修复 subUserModalApi.open 错误并传递父级用户ID

**文件**: `apps/web-antd/src/views/argame/user/index.vue`

**问题描述**:
1. 点击"查看下级"按钮时报错：`subUserModalApi.open is not a function`
2. 需要将当前行的 userId 传递给子用户模态框作为查询参数

**修改内容**:
1. 修复 `useVbenModal` 的解构赋值，正确获取 SubUserModal 组件
2. 在模板中添加 SubUserModal 组件以确保正确初始化
3. 修改 `handleImport` 函数接收行数据并传递给模态框
4. 更新按钮点击事件传递当前行数据

**修改前**:
```typescript
const [, subUserModalApi] = useVbenModal({
  connectedComponent: subUserModal,
});

function handleImport() {
  subUserModalApi.open();
}

@click="handleImport"
```

**修改后**:
```typescript
const [SubUserModal, subUserModalApi] = useVbenModal({
  connectedComponent: subUserModal,
});

function handleImport(row: Required<UserForm>) {
  subUserModalApi.setData({ parentUserId: row.userId });
  subUserModalApi.open();
}

@click="handleImport(row)"
```

**模板修改**:
```vue
<UserModal @reload="tableApi.query()" />
<SubUserModal @reload="tableApi.query()" />
```

**预期效果**:
- 点击"查看下级"按钮时不再报错
- 当前行的 userId 会作为 parentUserId 传递给子用户模态框
- 子用户模态框可以使用这个参数进行后端查询

### 修复 subUserModalApi.open 错误

**文件**: `apps/web-antd/src/views/argame/user/index.vue`

**问题描述**:
- 点击"查看下级"按钮时报错：`subUserModalApi.open is not a function`
- 原因是 `useVbenModal` 的解构赋值不正确，跳过了 Modal 组件导致 API 未正确初始化

**修改内容**:
1. 修复 `useVbenModal` 的解构赋值，正确获取 SubUserModal 组件
2. 在模板中添加 SubUserModal 组件以确保正确初始化

**修改前**:
```typescript
const [, subUserModalApi] = useVbenModal({
  connectedComponent: subUserModal,
});
```

**修改后**:
```typescript
const [SubUserModal, subUserModalApi] = useVbenModal({
  connectedComponent: subUserModal,
});
```

**模板修改**:
```vue
<UserModal @reload="tableApi.query()" />
<SubUserModal @reload="tableApi.query()" />
```

**预期效果**:
- 点击"查看下级"按钮时不再报错
- subUserModalApi.open() 方法可以正常调用
- 子用户模态框可以正常打开
