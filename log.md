# 修改日志

## 2025-08-24

### 修复 Vite 打包时 ant-design-vue 组件导入冲突问题

**文件**: `playground/src/adapter/component/index.ts`

**问题描述**: 
- 打包时报错：Button 组件被动态导入和静态导入，导致模块冲突
- 错误信息：`is dynamically imported by ... but also statically imported by ...`
- 原因是项目中同时存在动态导入和静态导入 ant-design-vue 组件

**修改内容**:
将所有 ant-design-vue 组件从动态导入改为统一的静态导入

**修改前**:
```typescript
const Button = defineAsyncComponent(() => import('ant-design-vue/es/button'));
const Checkbox = defineAsyncComponent(() => import('ant-design-vue/es/checkbox'));
// ... 其他动态导入
```

**修改后**:
```typescript
import {
  AutoComplete,
  Button,
  Checkbox,
  DatePicker,
  Divider,
  Input,
  InputNumber,
  Mentions,
  notification,
  Radio,
  Rate,
  Select,
  Space,
  Switch,
  TimePicker,
  TreeSelect,
  Upload,
} from 'ant-design-vue';

// 从组件中提取子组件
const CheckboxGroup = Checkbox.Group;
const InputPassword = Input.Password;
const RadioGroup = Radio.Group;
const RangePicker = DatePicker.RangePicker;
const Textarea = Input.TextArea;
```

**预期效果**:
- 解决 Vite 打包时的模块冲突问题
- 统一组件导入方式，避免动态导入和静态导入混用
- 打包过程不再报错，成功完成构建

**技术说明**:
- 动态导入 (`defineAsyncComponent`) 和静态导入不能混用同一个模块
- Vite 在打包时会检测到这种冲突并报错
- 统一使用静态导入可以避免这个问题，同时保持代码的一致性
