/* ant-design-vue 组件库的一些样式重置 */

.ant-app {
  width: 100%;
  height: 100%;
  overscroll-behavior: none;
  color: inherit;
}

.ant-btn {
  .anticon {
    display: inline-flex;
  }

  /* * 修复按钮添加图标时的位置问题 */
  > svg {
    display: inline-block;
  }

  > svg + span {
    margin-inline-start: 6px;
  }
}

.ant-tag {
  > svg {
    display: inline-block;
  }

  > svg + span {
    margin-inline-start: 4px;
  }
}

.ant-message-notice-content,
.ant-notification-notice {
  @apply dark:border-border/60 dark:border;
}

.form-valid-error {
  /** select 选择器的样式 */

  .ant-select:not(.valid-success) .ant-select-selector:not(.valid-success) {
    border-color: hsl(var(--destructive)) !important;
  }

  .ant-select-focused .ant-select-selector {
    box-shadow: 0 0 0 2px rgb(255 38 5 / 6%) !important;
  }

  /** 数字输入框样式 */
  .ant-input-number-focused {
    box-shadow: 0 0 0 2px rgb(255 38 5 / 6%);
  }

  /** 密码输入框样式 */
  .ant-input-affix-wrapper:hover {
    border-color: hsl(var(--destructive));
    box-shadow: 0 0 0 2px rgb(255 38 5 / 6%);
  }

  .ant-input:not(.valid-success) {
    border-color: hsl(var(--destructive)) !important;
  }
}

/** 区间选择器下面来回切换时的样式 */
.ant-app .form-valid-error .ant-picker-active-bar {
  background-color: hsl(var(--destructive));
}

/** 时间选择器的样式 */
.ant-app .form-valid-error .ant-picker-focused {
  box-shadow: 0 0 0 2px rgb(255 38 5 / 6%);
}

/** 前后置小圆点样式 */
.dot-before-common {
  @apply before:relative before:top-[-2px] before:mr-[5px] before:inline-block before:size-[6px] before:rounded-full before:content-[''];
}

.dot-before-red {
  @apply dot-before-common before:bg-red-500;
}

.dot-before-green {
  @apply dot-before-common before:bg-green-500;
}

/**
vxe表格右上角toolbar元素之间的间距
*/
.vxe-button + .vxe-button.type--button {
  margin-left: 8px !important;
}

/**
vxe默认圆角
*/
html {
  --vxe-ui-border-radius: 8px !important;
}

/**
vxe表格loading 只加载表格 不加载上面的表单
*/
.vxe-grid.is--loading::before {
  content: none !important;
}

/**
自定义success按钮样式
ghost按钮专用!
*/
.btn-success {
  color: hsl(var(--success)) !important;
  border-color: hsl(var(--success)) !important;
}

.btn-success:hover {
  color: hsl(var(--success) / 50%) !important;
  border-color: hsl(var(--success) / 50%) !important;
}

html.dark button[disabled].btn-success {
  color: rgb(242 242 242 / 25%) !important;
  border-color: hsl(240deg 3.7% 22%) !important;
}

button[disabled].btn-success {
  color: rgb(50 54 57 / 25%) !important;
  border-color: hsl(240deg 5.9% 90%) !important;
}
