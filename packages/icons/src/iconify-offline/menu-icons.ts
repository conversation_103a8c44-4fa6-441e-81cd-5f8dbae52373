import { addIcon } from '@vben-core/icons';

import schedule from '@iconify/icons-akar-icons/schedule';
import settingOutline from '@iconify/icons-ant-design/setting-outlined';
import antdTool from '@iconify/icons-ant-design/tool-outlined';
import UserAntd from '@iconify/icons-ant-design/user-outlined';
import Operation from '@iconify/icons-arcticons/one-hand-operation';
import BaseLineHousesFill from '@iconify/icons-bi/houses-fill';
import BxPackage from '@iconify/icons-bx/package';
import modelAlt from '@iconify/icons-carbon/model-alt';
import taskApproved from '@iconify/icons-carbon/task-approved';
import redisWordmark from '@iconify/icons-devicon/redis-wordmark';
import springWordmark from '@iconify/icons-devicon/spring-wordmark';
import vscode from '@iconify/icons-devicon/vscode';
import evergreenTree from '@iconify/icons-emojione/evergreen-tree';
import RoleBindingOutlined from '@iconify/icons-eos-icons/role-binding-outlined';
import SystemGroup from '@iconify/icons-eos-icons/system-group';
import NoticePush from '@iconify/icons-fe/notice-push';
import leave from '@iconify/icons-flat-color-icons/leave';
import plus from '@iconify/icons-flat-color-icons/plus';
import builDefinition from '@iconify/icons-fluent-mdl2/build-definition';
import Dictionary from '@iconify/icons-fluent-mdl2/dictionary';
import flow from '@iconify/icons-fluent-mdl2/flow';
import leaveUser from '@iconify/icons-fluent-mdl2/leave-user';
import from24 from '@iconify/icons-fluent/form-24-regular';
import BaseLineHouse from '@iconify/icons-ic/baseline-house';
import monitor from '@iconify/icons-ic/baseline-monitor';
import roundLaunch from '@iconify/icons-ic/round-launch';
import MenuSharp from '@iconify/icons-ic/sharp-menu';
import Appointment from '@iconify/icons-icon-park-outline/appointment';
import SettingTwo from '@iconify/icons-icon-park-twotone/setting-two';
import boolOpenText from '@iconify/icons-lucide/book-open-text';
import copyright from '@iconify/icons-lucide/copyright';
import table from '@iconify/icons-lucide/table';
import cloudDoneOutlineRounded from '@iconify/icons-material-symbols/cloud-done-outline-rounded';
import generatingTokensOutline from '@iconify/icons-material-symbols/generating-tokens-outline';
import LogoDevOutline from '@iconify/icons-material-symbols/logo-dev-outline';
import ccOutline from '@iconify/icons-mdi/cc-outline';
import tools from '@iconify/icons-mdi/tools';
import workflowOutline from '@iconify/icons-mdi/workflow-outline';
import DepartmentLine from '@iconify/icons-mingcute/department-line';
import profileLine from '@iconify/icons-mingcute/profile-line';
import UserDuotone from '@iconify/icons-ph/user-duotone';
import userList from '@iconify/icons-ph/user-list';
import users from '@iconify/icons-ph/users-light';
import insatnceLine from '@iconify/icons-ri/instance-line';
import todoLine from '@iconify/icons-ri/todo-line';
import Authy from '@iconify/icons-simple-icons/authy';
import FolderWithFilesOutline from '@iconify/icons-solar/folder-with-files-outline';
import monitorBoldDuotone from '@iconify/icons-solar/monitor-bold-duotone';
import monitorCameraOutlined from '@iconify/icons-solar/monitor-camera-outline';
import monitorPhoneOutlined from '@iconify/icons-solar/monitor-smartphone-outline';
import InterfaceLoginDialPadFingerPasswordDialPadDotFinger from '@iconify/icons-streamline/interface-login-dial-pad-finger-password-dial-pad-dot-finger';
import categoryPlus from '@iconify/icons-tabler/category-plus';
import code from '@iconify/icons-tabler/code';

/**
 * 这里添加菜单图标
 */
addIcon('eos-icons:system-group', SystemGroup);
addIcon('ph:user-duotone', UserDuotone);
addIcon('ant-design:user-outlined', UserAntd);
addIcon('eos-icons:role-binding-outlined', RoleBindingOutlined);
addIcon('ic:sharp-menu', MenuSharp);
addIcon('mingcute:department-line', DepartmentLine);
addIcon('icon-park-outline:appointment', Appointment);
addIcon('fluent-mdl2:dictionary', Dictionary);
addIcon('icon-park-twotone:setting-two', SettingTwo);
addIcon('fe:notice-push', NoticePush);
addIcon('material-symbols:logo-dev-outline', LogoDevOutline);
addIcon('arcticons:one-hand-operation', Operation);
addIcon(
  'streamline:interface-login-dial-pad-finger-password-dial-pad-dot-finger',
  InterfaceLoginDialPadFingerPasswordDialPadDotFinger,
);
addIcon('solar:folder-with-files-outline', FolderWithFilesOutline);
addIcon('simple-icons:authy', Authy);
addIcon('solar:monitor-smartphone-outline', monitorPhoneOutlined);
addIcon('ic:baseline-house', BaseLineHouse);
addIcon('ph:users-light', users);
addIcon('bi:houses-fill', BaseLineHousesFill);
addIcon('ph:user-list', userList);
addIcon('bx:package', BxPackage);
addIcon('solar:monitor-bold-duotone', monitorBoldDuotone);
addIcon('solar:monitor-camera-outline', monitorCameraOutlined);
addIcon('material-symbols:generating-tokens-outline', generatingTokensOutline);
addIcon('devicon:redis-wordmark', redisWordmark);
addIcon('devicon:spring-wordmark', springWordmark);
addIcon('akar-icons:schedule', schedule);
addIcon('mdi:tools', tools);
addIcon('ant-design:tool-outlined', antdTool);
addIcon('tabler:code', code);
addIcon('flat-color-icons:plus', plus);
addIcon('devicon:vscode', vscode);
addIcon('lucide:table', table);
addIcon('emojione:evergreen-tree', evergreenTree);
addIcon('fluent-mdl2:leave-user', leaveUser);
addIcon('mdi:workflow-outline', workflowOutline);
addIcon('tabler:category-plus', categoryPlus);
addIcon('carbon:model-alt', modelAlt);
addIcon('fluent-mdl2:build-definition', builDefinition);
addIcon('fluent-mdl2:build-definition', builDefinition);
addIcon('icon-park-outline:monitor', monitor);
addIcon('ri:instance-line', insatnceLine);
addIcon('ri:todo-line', todoLine);
addIcon('fluent:form-24-regular', from24);
addIcon('carbon:task-approved', taskApproved);
addIcon('ic:round-launch', roundLaunch);
addIcon('material-symbols:cloud-done-outline-rounded', cloudDoneOutlineRounded);
addIcon('mdi:cc-outline', ccOutline);
addIcon('lucide:book-open-text', boolOpenText);
addIcon('lucide:copyright', copyright);
// 个人中心
addIcon('mingcute:profile-line', profileLine);
// oss配置
addIcon('ant-design:setting-outlined', settingOutline);
// 请假
addIcon('flat-color-icons:leave', leave);
// flow
addIcon('fluent-mdl2:flow', flow);
