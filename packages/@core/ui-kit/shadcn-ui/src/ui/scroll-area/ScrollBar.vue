<script setup lang="ts">
import type { ScrollAreaScrollbarProps } from 'radix-vue';

import { cn } from '@vben-core/shared/utils';
import { ScrollAreaScrollbar, ScrollAreaThumb } from 'radix-vue';
import { computed } from 'vue';

const props = withDefaults(
  defineProps<{ class?: any } & ScrollAreaScrollbarProps>(),
  {
    orientation: 'vertical',
  },
);

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props;

  return delegated;
});
</script>

<template>
  <ScrollAreaScrollbar
    v-bind="delegatedProps"
    :class="
      cn(
        'flex touch-none select-none transition-colors',
        orientation === 'vertical' &&
          'h-full w-2.5 border-l border-l-transparent p-px',
        orientation === 'horizontal' &&
          'h-2.5 flex-col border-t border-t-transparent p-px',
        props.class,
      )
    "
  >
    <ScrollAreaThumb class="bg-border relative flex-1 rounded-full" />
  </ScrollAreaScrollbar>
</template>
