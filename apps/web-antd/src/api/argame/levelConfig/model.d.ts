import type { BaseEntity, PageQuery } from '#/api/common';

export interface LevelConfigVO {
  /**
   *  ID
   */
  levelId: number | string;

  /**
   * 等级名称
   */
  levelName: string;

  /**
   * 邀请人升级等级


   */
  inviteUpCount: number;

  /**
   * 备注信息
   */
  remark: string;

  /**
   * 身份类型 (0-会员,1-代理)
   */
  idType: number | string;

  /**
   * 排序
   */
  sorted: number;
}

export interface LevelConfigForm extends BaseEntity {
  /**
   *  ID
   */
  levelId?: number | string;

  /**
   * 等级名称
   */
  levelName?: string;

  /**
   * 邀请人升级等级


   */
  inviteUpCount?: number;

  /**
   * 备注信息
   */
  remark?: string;

  /**
   * 身份类型
   */
  idType?: number | string;

  /**
   * 排序
   */
  sorted?: number;
}

export interface LevelConfigQuery extends PageQuery {
  /**
   * 等级名称
   */
  levelName?: string;

  /**
   * 邀请人升级等级


   */
  inviteUpCount?: number;

  /**
   * 身份类型
   */
  idType?: number | string;

  /**
   * 排序
   */
  sorted?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
