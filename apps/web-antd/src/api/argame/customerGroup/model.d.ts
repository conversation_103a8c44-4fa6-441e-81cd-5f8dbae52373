import type { BaseEntity, PageQuery } from '#/api/common';

export interface CustomerGroupVO {
  /**
   * 客户组ID
   */
  customerGroupId: number | string;

  /**
   * 客户组名称
   */
  groupName: string;

  /**
   * 客户组描述
   */
  description: string;

  /**
   * 状态(0-禁用,1-正常)
   */
  status: string;

  /**
   * 排序序号
   */
  sortOrder: number;

  /**
   * 逻辑删除(0-未删,1-已删)
   */
  deleted: number;
}

export interface CustomerGroupForm extends BaseEntity {
  /**
   * 客户组ID
   */
  customerGroupId?: number | string;

  /**
   * 客户组名称
   */
  groupName?: string;

  /**
   * 客户组描述
   */
  description?: string;

  /**
   * 状态(0-禁用,1-正常)
   */
  status?: string;

  /**
   * 排序序号
   */
  sortOrder?: number;

  /**
   * 逻辑删除(0-未删,1-已删)
   */
  deleted?: number;
}

export interface CustomerGroupQuery extends PageQuery {
  /**
   * 客户组名称
   */
  groupName?: string;

  /**
   * 客户组描述
   */
  description?: string;

  /**
   * 状态(0-禁用,1-正常)
   */
  status?: string;

  /**
   * 排序序号
   */
  sortOrder?: number;

  /**
   * 逻辑删除(0-未删,1-已删)
   */
  deleted?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
