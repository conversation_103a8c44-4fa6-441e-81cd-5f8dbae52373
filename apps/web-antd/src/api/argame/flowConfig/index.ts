import type { FlowConfigForm, FlowConfigQuery, FlowConfigVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { requestClient } from '#/api/request';

/**
 * 查询流水配置列表
 * @param params
 * @returns 流水配置列表
 */
export function flowConfigList(params?: FlowConfigQuery) {
  return requestClient.get<PageResult<FlowConfigVO>>(
    '/system/flowConfig/list',
    { params },
  );
}

/**
 * 查询流水配置详情
 * @param flowConfigId id
 * @returns 流水配置详情
 */
export function flowConfigInfo(flowConfigId: ID) {
  return requestClient.get<FlowConfigVO>(`/system/flowConfig/${flowConfigId}`);
}

/**
 * 新增流水配置
 * @param data
 * @returns void
 */
export function flowConfigAdd(data: FlowConfigForm) {
  return requestClient.postWithMsg<void>('/system/flowConfig', data);
}

/**
 * 更新流水配置
 * @param data
 * @returns void
 */
export function flowConfigUpdate(data: FlowConfigForm) {
  return requestClient.putWithMsg<void>('/system/flowConfig', data);
}

/**
 * 删除流水配置
 * @param flowConfigId id
 * @returns void
 */
export function flowConfigRemove(flowConfigId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(
    `/system/flowConfig/${flowConfigId}`,
  );
}
