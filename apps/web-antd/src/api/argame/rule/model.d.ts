import type { PageQuery, BaseEntity } from '#/api/common';

export interface RuleVO {
  /**
   * 规则中心
   */
  ruleId: string | number;

  /**
   * 名称
   */
  name: string;

  /**
   * 规则key
   */
  ruleKey: string;

  /**
   * 类型,字典bill_rule_type（1富文本 2输入框 3链接）
   */
  type: string;

  /**
   * 内容
   */
  content: string;

  /**
   * 备注
   */
  remark: string;

}

export interface RuleForm extends BaseEntity {
  /**
   * 规则中心
   */
  ruleId?: string | number;

  /**
   * 名称
   */
  name?: string;

  /**
   * 规则key
   */
  ruleKey?: string;

  /**
   * 类型,字典bill_rule_type（1富文本 2输入框 3链接）
   */
  type?: string;

  /**
   * 内容
   */
  content?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface RuleQuery extends PageQuery {
  /**
   * 名称
   */
  name?: string;

  /**
   * 规则key
   */
  ruleKey?: string;

  /**
   * 类型,字典bill_rule_type（1富文本 2输入框 3链接）
   */
  type?: string;

  /**
   * 内容
   */
  content?: string;

  /**
    * 日期范围参数
    */
  params?: any;
}
