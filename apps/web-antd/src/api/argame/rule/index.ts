import type { RuleForm, RuleQuery, RuleVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询规则列表
 * @param params
 * @returns 规则列表
 */
export function ruleList(params?: RuleQuery) {
  return requestClient.get<PageResult<RuleVO>>('/system/rule/list', { params });
}

/**
 * 导出规则列表
 * @param params
 * @returns 规则列表
 */
export function ruleExport(params?: RuleQuery) {
  return commonExport('/system/rule/export', params ?? {});
}

/**
 * 查询规则详情
 * @param ruleId id
 * @returns 规则详情
 */
export function ruleInfo(ruleId: ID) {
  return requestClient.get<RuleVO>(`/system/rule/${ruleId}`);
}

/**
 * 新增规则
 * @param data
 * @returns void
 */
export function ruleAdd(data: RuleForm) {
  return requestClient.postWithMsg<void>('/system/rule', data);
}

/**
 * 更新规则
 * @param data
 * @returns void
 */
export function ruleUpdate(data: RuleForm) {
  return requestClient.putWithMsg<void>('/system/rule', data);
}

/**
 * 删除规则
 * @param ruleId id
 * @returns void
 */
export function ruleRemove(ruleId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/system/rule/${ruleId}`);
}
