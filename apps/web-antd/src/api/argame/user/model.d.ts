import type { BaseEntity, PageQuery } from '#/api/common';

export interface UserVO {
  /**
   * 用户ID
   */
  userId: number | string;

  /**
   * 手机号
   */
  phone: string;

  /**
   * 加密密码
   */
  password: string;

  /**
   * 用户名
   */
  username: string;

  /**
   * 用户类型(1-游客, 2-普通用户,3-机器人,4-运营)
   */
  userType: string;

  /**
   * 身份类型(0-会员,1-一级代理,。。。)
   */
  idType: number | string;

  /**
   * 身份类型等级
   */
  idTypeLevel: number | string;

  /**
   * 用户自身邀请码
   */
  inviteCode: string;

  /**
   * 邀请当前用户的邀请码
   */
  invitedByCode: string;

  /**
   * 上级用户ID
   */
  parentId: number | string;

  /**
   * 所属客服组ID
   */
  customerGroupId: number | string;

  /**
   * 头像URL
   */
  avatar: string;

  /**
   * 在线状态(0-离线,1-在线)
   */
  onlineStatus: string;

  /**
   * 最后登录时间
   */
  lastLoginTime: number;

  /**
   * 最后登录IP
   */
  lastLoginIp: string;

  /**
   * 账号状态(0-禁用,1-启用)
   */
  status: string;

  /**
   * 返佣配置(1-公共,2-独立)
   */
  returnType: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 逻辑删除(0-未删,1-已删)
   */
  deleted: number;
}

export interface UserForm extends BaseEntity {
  /**
   * 用户ID
   */
  userId?: number | string;

  /**
   * 手机号
   */
  phone?: string;

  /**
   * 加密密码
   */
  password?: string;

  /**
   * 用户名
   */
  username?: string;

  /**
   * 用户类型(1-游客, 2-普通用户,3-机器人,4-运营)
   */
  userType?: string;

  /**
   * 身份类型(0-会员,1-一级代理,。。。)
   */
  idType?: number | string;

  /**
   * 身份类型等级
   */
  idTypeLevel?: number | string;

  /**
   * 用户自身邀请码
   */
  inviteCode?: string;

  /**
   * 邀请当前用户的邀请码
   */
  invitedByCode?: string;

  /**
   * 上级用户ID
   */
  parentId?: number | string;

  /**
   * 所属客服组ID
   */
  customerGroupId?: number | string;

  /**
   * 头像URL
   */
  avatar?: string;

  /**
   * 在线状态(0-离线,1-在线)
   */
  onlineStatus?: string;

  /**
   * 最后登录时间
   */
  lastLoginTime?: number;

  /**
   * 最后登录IP
   */
  lastLoginIp?: string;

  /**
   * 账号状态(0-禁用,1-启用)
   */
  status?: string;

  /**
   * 返佣配置(1-公共,2-独立)
   */
  returnType?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 逻辑删除(0-未删,1-已删)
   */
  deleted?: number;

  /**
   * 开启提现
   */
  canDraw?: number | string;
}

export interface UserQuery extends PageQuery {
  /**
   * 手机号
   */
  phone?: string;

  /**
   * 加密密码
   */
  password?: string;

  /**
   * 用户名
   */
  username?: string;

  /**
   * 用户类型(1-游客, 2-普通用户,3-机器人,4-运营)
   */
  userType?: string;

  /**
   * 身份类型(0-会员,1-一级代理,。。。)
   */
  idType?: number | string;

  /**
   * 身份类型等级
   */
  idTypeLevel?: number | string;

  /**
   * 用户自身邀请码
   */
  inviteCode?: string;

  /**
   * 邀请当前用户的邀请码
   */
  invitedByCode?: string;

  /**
   * 上级用户ID
   */
  parentId?: number | string;

  /**
   * 所属客服组ID
   */
  customerGroupId?: number | string;

  /**
   * 头像URL
   */
  avatar?: string;

  /**
   * 在线状态(0-离线,1-在线)
   */
  onlineStatus?: string;

  /**
   * 最后登录时间
   */
  lastLoginTime?: number;

  /**
   * 最后登录IP
   */
  lastLoginIp?: string;

  /**
   * 账号状态(0-禁用,1-启用)
   */
  status?: string;

  /**
   * 返佣配置(1-公共,2-独立)
   */
  returnType?: string;

  /**
   * 逻辑删除(0-未删,1-已删)
   */
  deleted?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
