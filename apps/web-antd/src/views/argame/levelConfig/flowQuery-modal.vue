<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { FlowConfigForm } from '#/api/argame/flowConfig/model';

import { ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { getVxePopupContainer } from '@vben/utils';

import { Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { flowConfigList, flowConfigRemove } from '#/api/argame/flowConfig';

import { flowConfigColumns } from './data';
import levelRebateModal from './levelRebateConfig-modal.vue';

const levelId = ref<number | string>('');
const tabKey = ref<string>('member');

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    highlight: true,
    reserve: true,
  },
  columns: flowConfigColumns,
  keepSource: true,
  size: 'small',
  minHeight: 600,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const queryParams = {
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        };
        // 添加 levelId 参数到查询条件
        if (levelId.value) {
          queryParams.levelId = levelId.value;
        }
        // 添加 idType 参数到查询条件
        queryParams.idType = tabKey.value === 'member' ? 1 : 2;
        return await flowConfigList(queryParams);
      },
    },
  },
  rowConfig: {
    keyField: 'flowConfigId',
  },
  toolbarConfig: {
    enabled: true,
    custom: true,
    refresh: true,
    zoom: true,
  },
  id: 'flow-query-modal',
  cellClassName: 'cursor-pointer',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  gridOptions,
});

// 处理编辑
const handleEdit = (row: any) => {
  // 打开等级返佣配置模态框，用于编辑流水
  levelRebateModalApi
    .setData({
      ...row,
      tabKey: tabKey.value,
      levelId: levelId.value, // 传递levelId
    })
    .open();
};

async function handleDelete(row: Required<FlowConfigForm>) {
  await flowConfigRemove(row.flowConfigId);
  await tableApi?.reload();
}

// 处理新增流水
const handleAddFlow = () => {
  // 打开等级返佣配置模态框，用于新增流水
  levelRebateModalApi
    .setData({
      flowConfigId: undefined,
      flowName: '',
      idType: tabKey.value === 'member' ? 1 : 2, // 根据tabKey判断是会员还是代理
      remark: '',
      tabKey: tabKey.value,
      levelId: levelId.value, // 传递levelId
    })
    .open();
};

const [BasicModal, modalApi] = useVbenModal({
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    // 获取传入的数据
    const data = modalApi.getData();
    if (data?.levelId) {
      levelId.value = data.levelId;
    }
    if (data?.tabKey) {
      tabKey.value = data.tabKey;
    }
  },
  // 不设置 onConfirm 回调，这样就不会显示确认按钮
});

// 等级返佣配置模态框
const [LevelRebateModal, levelRebateModalApi] = useVbenModal({
  connectedComponent: levelRebateModal,
});

// 监听 levelId 变化，当有值时触发查询
watch(levelId, (newValue) => {
  if (newValue) {
    // 延迟执行确保表格已经初始化
    setTimeout(() => {
      tableApi.query();
    }, 200);
  }
});

// 处理等级返佣配置重新加载
const handleLevelRebateReload = () => {
  // 重新查询流水配置列表
  tableApi.query();
};
</script>

<template>
  <BasicModal
    class="w-[1000px]"
    :title="`${tabKey === 'member' ? '会员' : '代理'}流水配置`"
    :show-confirm-button="false"
    cancel-text="关闭"
  >
    <BasicTable>
      <template #toolbar-tools>
        <Space>
          <a-button
            type="primary"
            v-access:code="['argame:markCategory:add']"
            @click="handleAddFlow"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['argame:markCategory:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['argame:markCategory:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
      <template #levelRebate="{ row }">
        <div v-if="row.configJson" class="whitespace-pre-line">
          {{
            (() => {
              try {
                const config = Array.isArray(row.configJson)
                  ? row.configJson
                  : JSON.parse(row.configJson);
                if (Array.isArray(config)) {
                  // 根据 sorted 字段排序
                  const sortedConfig = config.sort(
                    (a, b) => a.sorted - b.sorted,
                  );
                  return sortedConfig
                    .map((item) => `LV${item.sorted}/${item.rebates}%`)
                    .join('\n');
                }
                return JSON.stringify(row.configJson);
              } catch {
                return JSON.stringify(row.configJson);
              }
            })()
          }}
        </div>
        <span v-else>-</span>
      </template>
    </BasicTable>
    <LevelRebateModal @reload="handleLevelRebateReload" />
  </BasicModal>
</template>
