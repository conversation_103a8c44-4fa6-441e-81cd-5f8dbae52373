<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { Button, InputNumber, message } from 'ant-design-vue';

import {
  flowConfigAdd,
  flowConfigInfo,
  flowConfigUpdate,
} from '#/api/argame/flowConfig';

const emit = defineEmits<{ reload: [] }>();
const modalData = ref<any>({});
const tabKey = ref<string>('list');

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

// 等级返佣配置数据
const levelRebateConfig = ref<
  Array<{
    name: string;
    rebates: number;
    sorted: number;
  }>
>([]);

// 表单数据
const formData = ref({
  flowName: '',
  configJson: '',
});

// 添加新的等级配置
const addLevelConfig = () => {
  const newLevel = levelRebateConfig.value.length;
  levelRebateConfig.value.push({
    name: `${newLevel}`, // 只使用纯数字
    rebates: 0,
    sorted: newLevel,
  });
};

// 删除等级配置
const removeLevelConfig = (index: number) => {
  levelRebateConfig.value.splice(index, 1);
  // 重新排序等级
  levelRebateConfig.value.forEach((item, idx) => {
    item.sorted = idx;
  });
};

// 更新配置JSON
const updateConfigJson = () => {
  formData.value.configJson = JSON.stringify(levelRebateConfig.value);
};

// 监听等级返佣变化
const onRebateChange = () => {
  updateConfigJson();
};

const [BasicModal, modalApi] = useVbenModal({
  class: 'w-[800px]',
  fullscreenButton: false,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);

    const data = modalApi.getData() as {
      flowConfigId?: number | string;
      id?: number | string;
      levelId?: number | string;
      tabKey?: string;
    };
    modalData.value = data;
    tabKey.value = data.tabKey || 'list';

    const id = data.id || data.flowConfigId;
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      // 如果是编辑模式，加载现有数据
      const response = await flowConfigInfo(id);
      const record = response.data || response; // 处理可能的数据结构

      formData.value = {
        flowName: record.flowName || '',
        configJson: record.configJson || '',
      };

      if (record.configJson) {
        try {
          const config = Array.isArray(record.configJson)
            ? record.configJson
            : JSON.parse(record.configJson);

          // 确保数据格式正确
          levelRebateConfig.value = Array.isArray(config)
            ? config.map((item, index) => ({
                name: item.name || `${index}`, // 只使用纯数字
                rebates: item.rebates || 0,
                sorted: item.sorted === undefined ? index : item.sorted,
              }))
            : [];
        } catch {
          levelRebateConfig.value = [];
        }
      } else {
        levelRebateConfig.value = [];
      }
    } else {
      // 新增模式，初始化默认数据
      formData.value = {
        flowName: '',
        configJson: '',
      };
      levelRebateConfig.value = [];
      // 新增模式下不需要立即更新configJson，因为数组为空
    }

    modalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    modalApi.lock(true);

    // 验证必填字段
    if (!formData.value.flowName || String(formData.value.flowName).trim() === '') {
      message.error('请输入流水名称');
      return;
    }

    if (levelRebateConfig.value.length === 0) {
      message.error('请至少添加一个等级配置');
      return;
    }

    // 更新配置JSON
    updateConfigJson();

    // 准备提交数据
    const submitData = {
      flowConfigId: isUpdate.value
        ? modalData.value.id || modalData.value.flowConfigId
        : undefined,
      idType: tabKey.value === 'member' ? '1' : '2', // 1-会员, 2-代理
      flowName: formData.value.flowName,
      configJson: levelRebateConfig.value,
      levelId: modalData.value.levelId || 0,
    };

    // 调用API保存数据
    await (isUpdate.value
      ? flowConfigUpdate(submitData)
      : flowConfigAdd(submitData));

    emit('reload');
    modalApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.lock(false);
  }
}
</script>

<template>
  <BasicModal :title="title">
    <div class="p-6">
      <!-- 流水配置 -->
      <div class="mb-6">
        <div class="mb-2">
          <span class="text-red-500">*</span>
          <span class="text-gray-900">流水</span>
        </div>
        <input
          v-model="formData.flowName"
          type="text"
          placeholder="请输入"
          class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <!-- 等级返佣配置 -->
      <div class="mb-6">
        <div class="mb-2">
          <span class="font-medium text-gray-900">等级返佣</span>
        </div>
        <div class="mb-4 text-sm text-gray-500">默认从0开始</div>

        <!-- 等级配置列表 -->
        <div class="space-y-4">
          <div
            v-if="levelRebateConfig.length === 0"
            class="py-8 text-center text-gray-500"
          >
            暂无等级配置，请点击下方"新增"按钮添加
          </div>
          <div
            v-for="(config, index) in levelRebateConfig"
            :key="index"
            class="rounded-md border border-gray-200 bg-gray-50 p-4"
          >
            <div class="mb-3 flex items-center justify-between">
              <div class="flex items-center">
                <span class="mr-1 text-red-500">*</span>
                <span class="text-gray-900">下级等级 {{ config.sorted }}</span>
              </div>
              <Button
                type="link"
                class="h-auto p-0 text-blue-500"
                @click="removeLevelConfig(index)"
              >
                删除
              </Button>
            </div>

            <div class="flex items-center">
              <span class="mr-1 text-red-500">*</span>
              <span class="mr-2 text-gray-900">返佣</span>
              <InputNumber
                v-model:value="config.rebates"
                :min="0"
                :max="100"
                :precision="2"
                class="w-32"
                @change="onRebateChange"
              />
              <span class="ml-2 text-gray-500">%</span>
            </div>
          </div>
        </div>

        <!-- 新增按钮 -->
        <Button
          type="link"
          class="mt-4 h-auto p-0 text-blue-500"
          @click="addLevelConfig"
        >
          新增
        </Button>
      </div>
    </div>
  </BasicModal>
</template>

<style scoped>
/* 自定义样式 */
</style>
