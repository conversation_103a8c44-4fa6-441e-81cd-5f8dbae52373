import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { computed } from 'vue';

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新

// export const columns: () => VxeGridProps['columns'] = () => [

export const proxyColumns = computed((): VxeGridProps['columns'] => [
  { type: 'checkbox', width: 60 },
  {
    title: ' ID',
    field: 'levelId',
    visible: false,
  },
  {
    title: '创建时间',
    field: 'createTime',
  },
  {
    title: '更新时间',
    field: 'updateTime',
  },
  {
    title: '等级名称',
    field: 'levelName',
  },
  {
    title: '邀请人升级等级',
    field: 'inviteUpCount',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
]);

export const memberColumns = computed((): VxeGridProps['columns'] => [
  { type: 'checkbox', width: 60 },
  {
    title: ' ID',
    field: 'levelId',
    visible: false,
  },
  {
    title: '创建时间',
    field: 'createTime',
  },
  {
    title: '更新时间',
    field: 'updateTime',
  },
  {
    title: '等级名称',
    field: 'levelName',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
]);

export const flowConfigColumns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '创建时间',
    field: 'createTime',
    width: 180,
  },
  {
    title: '更新时间',
    field: 'updateTime',
    width: 180,
  },
  {
    title: '流水',
    field: 'flowName',
    width: 120,
  },
  {
    title: '等级/返佣',
    field: 'configJson',
    width: 200,
    slots: { default: 'levelRebate' },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 200,
  },
];

export const levelModalSchema: FormSchemaGetter = () => [
  {
    label: ' ID',
    fieldName: 'levelId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '等级名称',
    fieldName: 'levelName',
    component: 'Input',
  },
  {
    label: '会员等级',
    fieldName: 'inviteUpCount',
    component: 'InputNumber',
    rules: 'required',
  },
  {
    label: '邀请人升级等级',
    fieldName: 'level',
    component: 'InputNumber',
    rules: 'required',
  },
];
