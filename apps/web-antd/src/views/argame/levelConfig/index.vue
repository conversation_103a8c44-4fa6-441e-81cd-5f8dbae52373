<script lang="ts" setup>
import type { VxeGridProps } from '#/adapter/vxe-table';
import type { LevelConfigForm } from '#/api/argame/levelConfig/model';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { getVxePopupContainer } from '@vben/utils';

import { Popconfirm, RadioButton, RadioGroup, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { levelConfigList, levelConfigRemove } from '#/api/argame/levelConfig';

import addLevelModal from './addLevel-modal.vue';
import addGroup from './currencyExchangeConfig-modal.vue';
import { memberColumns, proxyColumns } from './data';
import flowQueryModal from './flowQuery-modal.vue';
import levelRebateModal from './levelRebateConfig-modal.vue';

const hasData = ref(true);

const tabKey = ref('member');

const gridOptions: VxeGridProps = {
  columns: memberColumns.value,
  height: 800,
  toolbarConfig: {
    custom: true,
    refresh: true,
    zoom: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const params = {
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
          fromPage: tabKey.value,
          idType: tabKey.value === 'member' ? '0' : '1',
          isAsc: 'asc',
          orderByColumn: 'level',
        };
        const result = await levelConfigList(params);
        if (tabKey.value === 'member') {
          hasData.value = !(result.total > 0);
        }
        return result;
      },
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

const radioChange = (e: any) => {
  const val = e.target.value;

  // 切换表格列
  gridApi.setGridOptions({
    ...gridOptions,
    columns: val === 'member' ? memberColumns.value : proxyColumns.value,
  });
  gridApi?.formApi?.resetForm();
  gridApi?.reload();
};

const [AddGroup, addGroupApi] = useVbenModal({
  connectedComponent: addGroup,
});

const [LevelRebateModal, levelRebateModalApi] = useVbenModal({
  connectedComponent: levelRebateModal,
});

const [AddLevelModal, addLevelModalApi] = useVbenModal({
  connectedComponent: addLevelModal,
});

const [FlowQueryModal, flowQueryModalApi] = useVbenModal({
  connectedComponent: flowQueryModal,
});

const handleProxyAdd = () => {
  // 根据当前选中的tab来决定使用哪个模态框
  addLevelModalApi.setData({ tabKey: tabKey.value }).open();
};

const handleReload = () => {
  gridApi?.reload();
};
const handleEdit = (row: any) => {
  // 根据当前选中的tab来决定使用哪个模态框
  if (tabKey.value === 'member' || tabKey.value === 'proxy') {
    // 先弹出流水查询界面，传递当前行的levelId
    flowQueryModalApi
      .setData({
        tabKey: tabKey.value,
        levelId: row.levelId,
      })
      .open();
  } else {
    addGroupApi.setData({ ...row, tabKey: tabKey.value }).open();
  }
};

// 处理流水选择
const handleSelectFlow = (flowData: any) => {
  // 选择流水后，打开等级返佣配置界面
  levelRebateModalApi.setData({ ...flowData, tabKey: tabKey.value }).open();
};

async function handleDelete(row: Required<LevelConfigForm>) {
  await levelConfigRemove(row.levelId);
  await gridApi?.reload();
}
</script>

<template>
  <div class="flex h-full flex-col gap-3 p-5" style="padding-bottom: 12px">
    <RadioGroup
      v-model:value="tabKey"
      button-style="solid"
      @change="radioChange"
    >
      <RadioButton class="min-w-[100px] text-center" value="member">
        会员
      </RadioButton>
      <RadioButton class="min-w-[100px] text-center" value="proxy">
        代理
      </RadioButton>
    </RadioGroup>
    <div class="h-full overflow-y-hidden">
      <Grid class="h-[calc(100%-8px)]">
        <template #toolbar-tools>
          <Space>
            <a-button
              type="primary"
              v-access:code="['argame:registerBonusSetting:add']"
              @click="handleProxyAdd"
              v-if="tabKey === 'proxy' || (tabKey === 'member' && hasData)"
            >
              {{ $t('pages.common.add') }}
            </a-button>
          </Space>
        </template>
        <template #action="{ row }">
          <a-button
            type="link"
            :tooltip="false"
            size="small"
            @click="handleEdit(row)"
          >
            {{ '配置' }}
          </a-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <a-button
              :tooltip="false"
              size="small"
              type="link"
              v-access:code="['argame:registerBonusSetting:remove']"
              @click.stop=""
              v-if="tabKey === 'proxy'"
            >
              {{ '删除' }}
            </a-button>
          </Popconfirm>
        </template>
      </Grid>
    </div>
    <AddGroup @reload="handleReload" />
    <LevelRebateModal @reload="handleReload" />
    <AddLevelModal @reload="handleReload" />
    <FlowQueryModal @select-flow="handleSelectFlow" />
  </div>
</template>

<style lang="scss" scoped>
.link-item {
  word-break: break-all;
  cursor: pointer;
  margin-bottom: 12px;
  .copy_icon {
    transform: translateY(2px);
    display: inline-block;
  }
  .link-item-link {
    word-break: break-all;
  }
}
</style>
