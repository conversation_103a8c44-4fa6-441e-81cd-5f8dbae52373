import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { h } from 'vue';

import dayjs from 'dayjs';

import { z } from '#/adapter/form';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'userId',
    label: 'UID',
  },
  {
    component: 'Input',
    fieldName: 'username',
    label: '用户名',
  },
  {
    component: 'Input',
    fieldName: 'phone',
    label: '手机号',
  },
  {
    component: 'Input',
    fieldName: 'inviteCode',
    label: '邀请码',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('argame_user_id_type'),
    },
    fieldName: 'idType',
    label: '身份类型',
  },
  {
    component: 'Select',
    fieldName: 'idTypeLevel',
    label: '身份等级',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('argame_user_status'),
    },
    fieldName: 'status',
    label: '账号状态',
  },
  {
    component: 'Input',
    fieldName: 'parentId',
    label: '上级ID',
  },
  {
    component: 'Select',
    fieldName: 'customerGroupId',
    label: '客服组',
    componentProps: {
      options: [],
    },
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('argame_user_online_status'),
    },
    fieldName: 'onlineStatus',
    label: '在线状态',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('argame_user_type'),
    },
    fieldName: 'userType',
    label: '用户类型',
  },
  {
    component: 'RangePicker',
    fieldName: 'createTime',
    label: '注册日期',
  },
  {
    component: 'RangePicker',
    fieldName: 'lastLoginTime',
    label: '登录时间',
  },
  {
    component: 'Input',
    fieldName: 'lastLoginIp',
    label: 'IP地址',
  },
  {
    component: 'Select',
    componentProps: {
      options: getDictOptions('argame_user_return_type'),
    },
    fieldName: 'returnType',
    label: '返佣配置',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '注册时间/最后登录时间/最后登录IP',
    field: 'lastLoginTime',
    minWidth: 280,
    slots: {
      default: ({ row }) => {
        return [
          h(
            'div',
            {
              class: 'breeding-time-cell',
              style:
                'width: 100%; min-height: 120px; padding: 8px 0; display: flex; flex-direction: column; justify-content: center;',
            },
            [
              h(
                'div',
                { style: 'padding: 3px 0; text-align: center;' },
                `注册时间: ${row?.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '--'}`,
              ),
              h(
                'div',
                { style: 'padding: 3px 0; text-align: center;' },
                `最后登录时间: ${row?.lastLoginTime && Number(row.lastLoginTime) > 0 ? dayjs(Number(row.lastLoginTime)).format('YYYY-MM-DD HH:mm:ss') : '--'}`,
              ),
              h(
                'div',
                { style: 'padding: 3px 0; text-align: center;' },
                `最后登录IP: ${row?.lastLoginIp || '--'}`,
              ),
            ],
          ),
        ];
      },
    },
  },
  {
    title: '用户类型',
    field: 'userType',
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, 'argame_user_type');
      },
    },
    minWidth: 150,
  },
  {
    title: '身份类型',
    field: 'idType',
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, 'argame_user_id_type');
      },
    },
    minWidth: 150,
  },
  {
    title: 'UID',
    field: 'userId',
    minWidth: 200,
  },
  {
    title: '用户名',
    field: 'username',
    minWidth: 150,
  },
  {
    title: '手机号',
    field: 'phone',
    minWidth: 150,
  },
  {
    title: '邀请码',
    field: 'inviteCode',
    minWidth: 150,
  },
  {
    title: '上级信息',
    field: 'parentId',
    minWidth: 280,
    slots: {
      default: ({ row }) => {
        if (!row.parentInfo) return '--';
        return [
          h(
            'div',
            {
              class: 'breeding-time-cell',
              style:
                'width: 100%; min-height: 120px; padding: 8px 0; display: flex; flex-direction: column; justify-content: center;',
            },
            [
              h(
                'div',
                { style: 'padding: 3px 0; text-align: center;' },
                `UID: ${row.parentInfo?.userId || ''}`,
              ),
              h(
                'div',
                { style: 'padding: 3px 0; text-align: center;' },
                `用户名: ${row.parentInfo?.username || ''}`,
              ),
            ],
          ),
        ];
      },
    },
  },
  {
    title: '客服组',
    field: 'customerGroupName',
    minWidth: 150,
  },
  {
    title: '积分',
    field: 'points',
    minWidth: 150,
  },
  {
    title: '账户余额',
    field: 'accountBalance',
    minWidth: 150,
    slots: {
      default: ({ row }) => {
        if (!row.userFinanceVo) return '--';
        return row.userFinanceVo?.accountBalance;
      },
    },
  },
  {
    title: '提现待审核金额',
    field: 'pendingWithdrawal',
    minWidth: 150,
    slots: {
      default: ({ row }) => {
        if (!row.userFinanceVo) return '--';
        return row.userFinanceVo?.pendingWithdrawal;
      },
    },
  },
  {
    title: '累计充值金额',
    field: 'totalRecharge',
    minWidth: 150,
    slots: {
      default: ({ row }) => {
        if (!row.userFinanceVo) return '--';
        return row.userFinanceVo?.totalRecharge;
      },
    },
  },
  {
    title: '下级总人数',
    field: 'oneCount',
    minWidth: 150,
  },
  {
    title: '返佣配置',
    field: 'returnType',
    minWidth: 150,
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, 'argame_user_return_type');
      },
    },
  },
  {
    title: '在线状态',
    field: 'onlineStatus',
    minWidth: 150,
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, 'argame_user_online_status');
      },
    },
  },
  {
    title: '账号状态',
    field: 'status',
    minWidth: 150,
    slots: {
      default: ({ row }) => {
        return renderDict(row.status, 'argame_user_status');
      },
    },
  },
  {
    title: '备注',
    field: 'remark',
    minWidth: 150,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 360,
    showOverflow: false,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '用户ID',
    fieldName: 'userId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '区号',
    fieldName: 'areaCode',
    component: 'Select',
    rules: 'selectRequired',
    dependencies: {
      disabled: (values) => {
        // 当userId不为空时禁用区号字段
        return values.userId;
      },
      triggerFields: ['userId'],
    },
  },
  {
    label: '手机号',
    fieldName: 'phone',
    component: 'Input',
    rules: 'required',
    dependencies: {
      disabled: (values) => {
        // 当userId不为空时禁用区号字段
        return values.userId;
      },
      triggerFields: ['userId'],
    },
  },
  {
    label: '密码',
    fieldName: 'password',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '确认密码',
    fieldName: 'confirmPassword',
    component: 'Input',
    rules: 'required',
    dependencies: {
      rules(values) {
        return z
          .string({ message: '请输入确认密码' })
          .min(1, { message: '请输入确认密码' })
          .refine((value) => value === values.password, '两次输入的密码不一致');
      },
      triggerFields: ['password'],
    },
  },
  {
    label: '客服组',
    fieldName: 'customerGroupId',
    component: 'Select',
    componentProps: {},
    rules: 'selectRequired',
  },
  {
    label: '用户类型',
    fieldName: 'userType',
    component: 'Select',
    rules: 'selectRequired',
    componentProps: {
      options: getDictOptions('argame_user_type'),
    },
  },
  {
    label: '身份类型',
    fieldName: 'idType',
    component: 'Select',
    rules: 'selectRequired',
    componentProps: {
      options: getDictOptions('argame_user_id_type'),
    },
  },
  {
    label: '代理等级',
    fieldName: 'levelId',
    component: 'ApiSelect',
    rules: 'selectRequired',
    componentProps: {
      api: async () => {
        const { levelConfigList } = await import('#/api/argame/levelConfig');
        const result = await levelConfigList({
          pageNum: 1,
          pageSize: 1000,
          idType: '1', // 代理类型
        });
        return result.rows.map((item) => ({
          label: item.levelName,
          value: item.levelId,
        }));
      },
      immediate: false, // 不立即加载
    },
    dependencies: {
      show: (values) => {
        const idType = String(values.idType);
        return idType === '1';
      },
      triggerFields: ['idType'],
    },
  },
  {
    label: '邀请码',
    fieldName: 'inviteCode',
    component: 'Input',
    dependencies: {
      show: (values) => {
        // 当userId不为空时禁用区号字段
        return !values.userId;
      },
      triggerFields: ['userId'],
    },
  },
];

export const balanceSchema: FormSchemaGetter = () => [
  {
    label: 'UID',
    fieldName: 'userId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '类型',
    fieldName: 'balanceType',
    component: 'Select',
    componentProps: {
      allowClear: true,
      options: [
        { label: '账户充值', value: '1' },
        { label: '账户出金', value: '2' },
        { label: '平台奖励', value: '3' },
      ],
    },
    rules: 'required',
  },
  {
    label: '金额',
    fieldName: 'balance',
    component: 'InputNumber',
    rules: 'required',
  },
  {
    label: '原因',
    fieldName: 'remark',
    component: 'Input',
    rules: 'required',
    dependencies: {
      if: (value: Partial<Record<string, any>>) => {
        return value.balanceType === '2' || value.balanceType === '3';
      },
      triggerFields: ['balanceType'],
    },
  },
];

export const pointsSchema: FormSchemaGetter = () => [
  {
    label: 'UID',
    fieldName: 'userId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '积分',
    fieldName: 'points',
    component: 'InputNumber',
    rules: 'required',
  },
];
