<script setup lang="ts">
import { computed, ref } from 'vue';
import { Form } from 'ant-design-vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { getKycInfo, kycApprove, kycReject } from '#/api/ranch/custom/kyc';
import { defaultFormValueGetter, useBeforeCloseDiff } from '#/utils/popup';

const emit = defineEmits<{ reload: [] }>();

import {kysInfoSchema} from '../data';

const rejectVisible = ref(false);
const rejectReason = ref('');
const submitting = ref(false);
const rejectFormRef = ref();

const rejectFormRules = {
  rejectReason: [{ required: true, message: $t('custom.rejectionReasonModalTitle'), trigger: 'blur' }]
};

const title = computed(() => {
  return $t('custom.actionsBtn.kycReview');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 120,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: kysInfoSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: defaultFormValueGetter(formApi),
    currentGetter: defaultFormValueGetter(formApi),
  },
);

const [BasicModal, modalApi] = useVbenModal({
  // 在这里更改宽度
  class: 'w-[800px]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    modalApi.modalLoading(true);

    const { customId } = modalApi.getData() as { customId?: number | string };
    const record = await getKycInfo({ customId });
    await formApi.setValues(record);
    await markInitialized();

    modalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    modalApi.lock(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    const data = cloneDeep(await formApi.getValues());
    const { customId } = modalApi.getData() as { customId: string };

    if (data.status === '2' && !data.rejectReason) {
      return;
    }

    if (data.status === '2') {
      await kycReject({ customId, rejectReason: data.rejectReason });
    } else {
      await kycApprove({ customId });
    }

    handleClosed();
    emit('reload');
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.lock(false);
  }
}

async function handleApprove() {
  try {
    modalApi.lock(true);
    const { customId } = modalApi.getData() as { customId: string };
    await kycApprove({ customId });
    handleClosed();
    emit('reload');
  } catch (error) {
    console.error(error);
  } finally {
    modalApi.lock(false);
  }
}

async function handleReject() {
  rejectVisible.value = true;
}

async function handleRejectConfirm() {
  try {
    await rejectFormRef.value?.validate();
    submitting.value = true;
    const { customId } = modalApi.getData() as { customId: string };
    await kycReject({ customId, rejectReason: rejectReason.value });
    rejectVisible.value = false;
    handleClosed();
    emit('reload');
  } catch (error) {
    console.error(error);
  } finally {
    submitting.value = false;
  }
}

function handleRejectCancel() {
  rejectVisible.value = false;
  rejectReason.value = '';
  rejectFormRef.value?.resetFields();
}

function handleClosed() {
  resetInitialized();
  formApi.resetForm();
  modalApi.close();
  rejectReason.value = '';
  rejectVisible.value = false;
  rejectFormRef.value?.resetFields();
}

defineExpose({
  handleApprove,
  handleReject
});
</script>

<template>
  <BasicModal :title="title">
    <BasicForm />
    <template #footer>
      <div class="flex justify-end gap-2">
        <a-button type="dashed" @click="handleReject">{{$t('custom.rejectButton')}}</a-button>
        <a-button type="primary" @click="handleApprove">{{$t('custom.approveButton')}}</a-button>
      </div>
    </template>
  </BasicModal>

  <a-modal
    v-model:visible="rejectVisible"
    :title="$t('custom.rejectionReasonModalTitle')"
    @ok="handleRejectConfirm"
    @cancel="handleRejectCancel"
    :maskClosable="false"
    :width="520"
  >
    <a-form
      ref="rejectFormRef"
      :model="{ rejectReason }"
      :rules="rejectFormRules"
      class="px-4 py-2"
    >
      <a-form-item
        name="rejectReason"
        label="拒绝原因"
        class="mb-0"
      >
        <a-textarea
          v-model:value="rejectReason"
          :placeholder="$t('custom.rejectionReasonModalTitle')"
          :rows="4"
          :maxlength="500"
          show-count
          class="!min-h-[120px]"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <div class="flex justify-end gap-3 px-4">
        <a-button @click="handleRejectCancel">{{$t('custom.cancelButton')}}</a-button>
        <a-button type="primary" :loading="submitting" @click="handleRejectConfirm">
          {{$t('custom.confirmButton')}}
        </a-button>
      </div>
    </template>
  </a-modal>
</template>
