<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { userList } from '#/api/argame/user';

import { columns, querySchema } from '../data';

// 过滤掉操作列，用于下级用户查看页面
const subUserColumns = columns.filter((col) => col.field !== 'action');

// 过滤掉上级用户ID查询字段，用于下级用户查看页面
const subUserQuerySchema = querySchema().filter(
  (field) => field.fieldName !== 'parentId',
);

const parentUserId = ref<string>('');

const formOptions: VbenFormProps = {
  schema: subUserQuerySchema,
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  showCollapseButton: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    highlight: true,
    reserve: true,
  },
  columns: subUserColumns,
  keepSource: true,
  size: 'small',
  minHeight: 800,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        const queryParams = {
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        };
        // 只有当 parentUserId 有值时才添加 parentId 参数
        if (parentUserId.value) {
          queryParams.parentId = parentUserId.value;
        }
        return await userList(queryParams);
      },
    },
  },
  rowConfig: {
    keyField: 'userId',
  },
  toolbarConfig: {
    enabled: false,
  },
  id: 'sub-user-modal',
  cellClassName: 'cursor-pointer',
};

const [BasicTable, tableApi] = useVbenVxeGrid({ formOptions, gridOptions });

const [BasicModal, modalApi] = useVbenModal({
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    // 获取传入的父级用户ID
    const data = modalApi.getData();
    if (data?.parentUserId) {
      parentUserId.value = data.parentUserId;
    }
  },
});

// 监听 parentUserId 变化，当有值时触发查询
watch(parentUserId, (newValue) => {
  if (newValue) {
    // 延迟执行确保表格已经初始化
    setTimeout(() => {
      tableApi.query();
    }, 200);
  }
});
</script>

<template>
  <BasicModal
    class="w-[1400px]"
    title="下级用户列表"
    :show-ok="false"
    cancel-text="关闭"
  >
    <BasicTable />
  </BasicModal>
</template>
