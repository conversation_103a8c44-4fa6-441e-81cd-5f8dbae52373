<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { userList } from '#/api/argame/user';

import { columns, querySchema } from '../data';

const parentUserId = ref<string>('');

const formOptions: VbenFormProps = {
  schema: querySchema(),
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  showCollapseButton: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    highlight: true,
    reserve: true,
  },
  columns,
  keepSource: true,
  size: 'small',
  minHeight: 800,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await userList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          parentId: parentUserId.value,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'userId',
  },
  toolbarConfig: {
    enabled: false,
  },
  id: 'sub-user-modal',
  cellClassName: 'cursor-pointer',
};

const [BasicTable, tableApi] = useVbenVxeGrid({ formOptions, gridOptions });

const [BasicModal, modalApi] = useVbenModal({
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    // 获取传入的父级用户ID
    console.log("parentUserId", parentUserId.value);
    const data = modalApi.getData();
    if (data?.parentUserId) {
      parentUserId.value = data.parentUserId;
      // 刷新表格数据
      await tableApi.query();
    }
  },
});
</script>

<template>
  <BasicModal class="w-[1400px]" title="下级用户列表" :show-ok="false">
    <BasicTable />
  </BasicModal>
</template>
