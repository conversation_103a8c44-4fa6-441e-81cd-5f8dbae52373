<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import {bankInfoInfoByCustomId, bankInfoUpdate,bankInfoAdd} from '#/api/ranch/bankInfo';
import { defaultFormValueGetter, useBeforeCloseDiff } from '#/utils/popup';

import {bankInfoSchema} from '../data';
import { language, $t } from '#/locales';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return $t('custom.actionsBtn.bankInfo');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: language.value === 'zh-CN' ? 80 : 150,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: bankInfoSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: defaultFormValueGetter(formApi),
    currentGetter: defaultFormValueGetter(formApi),
  },
);

const [BasicModal, bankInfoModalApi] = useVbenModal({
  // 在这里更改宽度
  class: 'w-[550px]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    bankInfoModalApi.modalLoading(true);

    const {customId} = bankInfoModalApi.getData() as { customId?: number | string };

    const record = await bankInfoInfoByCustomId(customId);
    record.customId = customId;
    await formApi.setValues(record);
    isUpdate.value = !!record.id;
    await markInitialized();

    bankInfoModalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    bankInfoModalApi.lock(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());
    await (isUpdate?.value ? bankInfoUpdate(data) :bankInfoAdd(data));
    resetInitialized();
    emit('reload');
    bankInfoModalApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    bankInfoModalApi.lock(false);
  }
}

async function handleClosed() {
  await formApi.resetForm();
  resetInitialized();
}
</script>

<template>
  <BasicModal :title="title" class="h-[calc(100vh-200px)] w-[1000px]">
    <BasicForm />
  </BasicModal>
</template>
