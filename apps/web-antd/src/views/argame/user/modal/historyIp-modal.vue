<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { useBeforeCloseDiff } from '#/utils/popup';
import type { ID } from '#/api/common';

// 定义IP历史记录接口
interface IpHistoryRecord {
  id?: string | number;
  createTime: string;
  ipAddress: string;
}

// 客户名
const customerName = ref('');
// 加载状态
const loading = ref(false);
// IP历史记录
const ipHistoryList = ref<IpHistoryRecord[]>([]);

const title = computed(() => $t('custom.historicalIPaddress'));

// 必须提供一个空的对象作为参数
const { resetInitialized } = useBeforeCloseDiff({
  initializedGetter: () => '',
  currentGetter: () => '',
});

// 直接生成一些固定的示例数据
const staticMockData: IpHistoryRecord[] = [
  { id: 1, createTime: '2023-09-09 12:12:11', ipAddress: 'ipv4' },
  { id: 2, createTime: '2023-09-09 12:12:11', ipAddress: 'ipv6' },
];

const [BasicModal, historyIpModalApi] = useVbenModal({
  class: 'w-[600px]',
  fullscreenButton: false,
  onClosed: handleClosed,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    historyIpModalApi.modalLoading(true);

    const { customId, customName } = historyIpModalApi.getData() as {
      customId?: ID;
      customName?: string;
    };

    if (customName) {
      customerName.value = customName;
    }

    await fetchHistoryIp();
    historyIpModalApi.modalLoading(false);
  },
});

// 获取IP历史记录
async function fetchHistoryIp() {
  loading.value = true;
  try {
    // 直接使用静态数据
    ipHistoryList.value = staticMockData;
    console.log('IP历史记录数据:', ipHistoryList.value);
  } catch (error) {
    console.error('获取历史IP记录失败:', error);
    ipHistoryList.value = [];
  } finally {
    loading.value = false;
  }
}

function handleClosed() {
  resetInitialized();
  customerName.value = '';
  ipHistoryList.value = [];
}
</script>

<template>
  <BasicModal
    :title="title"
    :footer="false"
  >
    <div v-if="loading" class="flex items-center justify-center p-4">
      <a-spin />
    </div>
    <div v-else class="px-4">
      <!-- 无数据提示 -->
      <div v-if="ipHistoryList.length === 0" class="text-center py-4 text-gray-500">
        暂无历史IP记录
      </div>

      <!-- 使用基本HTML表格 -->
      <div v-else>
        <table class="w-full border border-collapse">
          <thead>
            <tr class="bg-gray-100">
              <th class="border p-2 text-left">日期</th>
              <th class="border p-2 text-left">IP地址</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="record in ipHistoryList" :key="record.id || record.createTime" class="border-b">
              <td class="border p-2">{{ record.createTime }}</td>
              <td class="border p-2">{{ record.ipAddress }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </BasicModal>
</template>
