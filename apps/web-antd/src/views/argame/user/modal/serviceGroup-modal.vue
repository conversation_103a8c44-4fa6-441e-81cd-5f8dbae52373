<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import { customAdd, customInfo, customUpdate } from '#/api/ranch/custom';
import { supportGroupList } from "#/api/ranch/supportGroup";
import { defaultFormValueGetter, useBeforeCloseDiff } from '#/utils/popup';
import { $t } from '#/locales';

import { serviceGroupSchema } from '../data';

const emit = defineEmits<{ reload: [] }>();

// 客户名
const customerName = ref('');
// 加载状态
const loading = ref(false);

const isUpdate = ref(false);
const title = computed(() => {
  return $t('custom.actionsBtn.updateServiceGroup');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 80,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: serviceGroupSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

// 获取客服组列表
async function fetchServiceGroups() {
  loading.value = true;
  try {
    // 客服组
    const response = await supportGroupList({
      pageNum: 1,
      pageSize: 1000,
    });

    // 将客服组数据转换为选项格式
    const options = response.rows.map((item) => ({
      label: item.groupName,
      value: item.supportGroupId,
    }));

    // 更新表单schema中的客服组选项
    formApi.updateSchema([
      {
        componentProps: {
          options,
        },
        fieldName: 'serviceTeamId',
      },
    ]);
  } catch (error) {
    console.error('获取客服组列表失败:', error);
  } finally {
    loading.value = false;
  }
}

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: defaultFormValueGetter(formApi),
    currentGetter: defaultFormValueGetter(formApi),
  },
);

const [BasicModal, serviceGroupModalApi] = useVbenModal({
  // 在这里更改宽度
  class: 'w-[550px]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    serviceGroupModalApi.modalLoading(true);

    const { customId, customName } = serviceGroupModalApi.getData() as {
      customId?: number | string;
      customName?: string;
    };

    if (customName) {
      customerName.value = customName;
    }

    isUpdate.value = !!customId;

    // 先获取客服组列表
    await fetchServiceGroups();

    if (isUpdate.value && customId) {
      const record = await customInfo(customId);
      await formApi.setValues(record);
    }

    await markInitialized();
    serviceGroupModalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    serviceGroupModalApi.lock(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());
    await (isUpdate.value ? customUpdate(data) : customAdd(data));
    resetInitialized();
    emit('reload');
    serviceGroupModalApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    serviceGroupModalApi.lock(false);
  }
}

async function handleClosed() {
  await formApi.resetForm();
  resetInitialized();
  customerName.value = '';
}
</script>

<template>
  <BasicModal :title="title">
    <div v-if="customerName" class="mb-4 px-4">
      <span class="font-medium">{{$t('custom.userName')}}：</span>
      <span>{{ customerName }}</span>
    </div>
    <div v-if="loading" class="flex items-center justify-center p-4">
      <a-spin />
    </div>
    <BasicForm />
  </BasicModal>
</template>
