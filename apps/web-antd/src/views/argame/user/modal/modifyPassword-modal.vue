<script setup lang="ts">
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import { useVbenForm } from '#/adapter/form';
import {customInfo, resetPassword} from '#/api/ranch/custom';
import { defaultFormValueGetter, useBeforeCloseDiff } from '#/utils/popup';
import { $t } from '@vben/locales';

import {modifyPasswordSchema} from '../data';

const emit = defineEmits<{ reload: [] }>();

// 客户名
const customerName = ref('');
// 加载状态
const loading = ref(false);

const isUpdate = ref(false);
const title = computed(() => {
  return $t('custom.actionsBtn.changePassword');
});

const [BasicForm, formApi] = useVbenForm({
  commonConfig: {
    // 默认占满两列
    formItemClass: 'col-span-2',
    // 默认label宽度 px
    labelWidth: 80,
    // 通用配置项 会影响到所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  schema: modifyPasswordSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: defaultFormValueGetter(formApi),
    currentGetter: defaultFormValueGetter(formApi),
  },
);

const [BasicModal, serviceGroupModalApi] = useVbenModal({
  // 在这里更改宽度
  class: 'w-[550px]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    serviceGroupModalApi.modalLoading(true);

    const { customId } = serviceGroupModalApi.getData() as {
      customId?: number | string;
    };
    isUpdate.value = !!customId;
    if (isUpdate.value && customId) {
      const record = await customInfo(customId);
      record.password=null;
      await formApi.setValues(record);
    }
    await markInitialized();
    serviceGroupModalApi.modalLoading(false);
  },
});

async function handleConfirm() {
  try {
    serviceGroupModalApi.lock(true);
    const { valid } = await formApi.validate();
    if (!valid) {
      return;
    }
    // getValues获取为一个readonly的对象 需要修改必须先深拷贝一次
    const data = cloneDeep(await formApi.getValues());
    await (resetPassword(data));
    resetInitialized();
    emit('reload');
    serviceGroupModalApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    serviceGroupModalApi.lock(false);
  }
}

async function handleClosed() {
  await formApi.resetForm();
  resetInitialized();
  customerName.value = '';
}
</script>

<template>
  <BasicModal :title="title">
    <BasicForm />
  </BasicModal>
</template>
