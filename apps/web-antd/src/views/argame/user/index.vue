<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { UserForm } from '#/api/argame/user/model';

import { onMounted } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { getVxePopupContainer } from '@vben/utils';

import { Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { customerGroupList } from '#/api/argame/customerGroup';
import { userList, userUpdate } from '#/api/argame/user';

import { columns, querySchema } from './data';
import subUserModal from './modal/sub-user-modal.vue';
import userModal from './user-modal.vue';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  // 处理区间选择器RangePicker时间格式 将一个字段映射为两个字段 搜索/导出会用到
  // 不需要直接删除
  // fieldMappingTime: [
  //  [
  //    'createTime',
  //    ['params[beginTime]', 'params[endTime]'],
  //    ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
  //  ],
  // ],
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await userList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'userId',
  },
  cellConfig: {
    height: 150,
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'argame-user-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [UserModal, modalApi] = useVbenModal({
  connectedComponent: userModal,
});
const [SubUserModal, subUserModalApi] = useVbenModal({
  connectedComponent: subUserModal,
});

function handleAdd() {
  modalApi.setData({});
  modalApi.open();
}

async function handleEdit(row: Required<UserForm>) {
  modalApi.setData({ id: row.userId });
  modalApi.open();
}

async function loadSupportGroup() {
  const attrRes = await customerGroupList({
    pageNum: 1,
    pageSize: 1000,
    status: '1',
  });
  const serviceTeamOptions = attrRes.rows.map((item) => ({
    label: item.groupName,
    value: item.customerGroupId,
  }));
  tableApi.formApi.updateSchema([
    {
      componentProps: {
        options: serviceTeamOptions,
      },
      fieldName: 'customerGroupId',
    },
  ]);
}
async function handleUpdateStatus(row: Required<UserForm>) {
  await userUpdate({
    userId: row.userId,
    status: row.status === '0' ? '1' : '1',
  });
  await tableApi.query();
}

async function handleUpdateCanDraw(row: Required<UserForm>) {
  await userUpdate({
    userId: row.userId,
    canDraw: row.canDraw === '0' ? '1' : '0',
  });
  await tableApi.query();
}

function handleImport() {
  subUserModalApi.open();
}

onMounted(() => {
  // 初始配置
  loadSupportGroup();
});
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="用户列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            type="primary"
            v-access:code="['argame:user:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <div class="flex flex-wrap" style="gap: 0; margin: -2px">
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确定要停用吗？"
            @confirm="handleUpdateStatus(row)"
          >
            <a-button
              :tooltip="false"
              size="small"
              type="link"
              v-access:code="['ranch:product:edit']"
              @click.stop=""
              v-if="row.status === '1'"
            >
              {{ '禁用账号' }}
            </a-button>
          </Popconfirm>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确定要启用吗？"
            @confirm="handleUpdateStatus(row)"
          >
            <a-button
              :tooltip="false"
              size="small"
              type="link"
              v-access:code="['ranch:product:edit']"
              @click.stop=""
              v-if="row.status === '0'"
            >
              {{ '启用账号' }}
            </a-button>
          </Popconfirm>

          <a-button
            :tooltip="false"
            size="small"
            type="link"
            v-access:code="['argame:user:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </a-button>

          <a-button
            :tooltip="false"
            size="small"
            type="link"
            v-access:code="['argame:user:edit']"
            @click="handleImport"
          >
            {{ '查看下级' }}
          </a-button>

          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            :title="$t('custom.confirm.enableWithdraw')"
            @confirm="handleUpdateCanDraw(row)"
          >
            <a-button
              :tooltip="false"
              size="small"
              type="link"
              v-access:code="['ranch:product:edit']"
              @click.stop=""
              v-if="row.canDraw === '1'"
            >
              {{ '关闭提现' }}
            </a-button>
          </Popconfirm>

          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            :title="$t('custom.confirm.disableWithdraw')"
            @confirm="handleUpdateCanDraw(row)"
          >
            <a-button
              :tooltip="false"
              size="small"
              type="link"
              v-access:code="['ranch:product:edit']"
              @click.stop=""
            >
              {{ '开启提现' }}
            </a-button>
          </Popconfirm>

          <!--          <a-button-->
          <!--            :tooltip="false"-->
          <!--            size="small"-->
          <!--            type="link"-->
          <!--            v-access:code="['ranch:breedingOrder:edit']"-->
          <!--            @click.stop="handleBalance(row)"-->
          <!--          >-->
          <!--            {{ $t('custom.actionsBtn.balance') }}-->
          <!--          </a-button>-->
          <!--          <a-button-->
          <!--            :tooltip="false"-->
          <!--            size="small"-->
          <!--            type="link"-->
          <!--            v-access:code="['ranch:breedingOrder:edit']"-->
          <!--            @click.stop="handlePoints(row)"-->
          <!--          >-->
          <!--            {{ $t('custom.actionsBtn.addPoints') }}-->
          <!--          </a-button>-->
          <!--          <a-button-->
          <!--            :tooltip="false"-->
          <!--            size="small"-->
          <!--            type="link"-->
          <!--            v-access:code="['ranch:breedingOrder:edit']"-->
          <!--            @click.stop="handleRemark(row)"-->
          <!--          >-->
          <!--            {{ $t('custom.actionsBtn.remark') }}-->
          <!--          </a-button>-->
        </div>
      </template>
    </BasicTable>
    <UserModal @reload="tableApi.query()" />
    <SubUserModal @reload="tableApi.query()" />
  </Page>
</template>
