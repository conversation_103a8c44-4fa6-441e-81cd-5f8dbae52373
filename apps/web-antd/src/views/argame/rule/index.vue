<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { RuleForm } from '#/api/argame/rule/model';

import { Page, useVbenModal } from '@vben/common-ui';
import { useI18n } from '@vben/locales';
import { getVxePopupContainer } from '@vben/utils';

import { Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { ruleList, ruleRemove } from '#/api/argame/rule';

import { columns, querySchema } from './data';
import ruleModal from './rule-modal.vue';

const { t } = useI18n();

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  // 处理区间选择器RangePicker时间格式 将一个字段映射为两个字段 搜索/导出会用到
  // 不需要直接删除
  // fieldMappingTime: [
  //  [
  //    'createTime',
  //    ['params[beginTime]', 'params[endTime]'],
  //    ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
  //  ],
  // ],
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  columns: columns.value,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await ruleList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'ruleId',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'ranch-rule-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [RuleModal, modalApi] = useVbenModal({
  connectedComponent: ruleModal,
});

function handleAdd() {
  modalApi.setData({});
  modalApi.open();
}

async function handleEdit(row: Required<RuleForm>) {
  modalApi.setData({ id: row.ruleId });
  modalApi.open();
}

async function handleDelete(row: Required<RuleForm>) {
  await ruleRemove(row.ruleId);
  await tableApi.query();
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable :table-title="t('rule.table.title')">
      <template #toolbar-tools>
        <Space>
          <a-button
            type="primary"
            v-access:code="['ranch:rule:add']"
            @click="handleAdd"
          >
            {{ t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['ranch:rule:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            :title="t('pages.common.deleteConfirm')"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['ranch:rule:remove']"
              @click.stop=""
            >
              {{ t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
    </BasicTable>
    <RuleModal @reload="tableApi.query()" />
  </Page>
</template>
