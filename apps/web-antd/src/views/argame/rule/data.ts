import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { computed } from 'vue';

import { $t } from '@vben/locales';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'name',
    label: $t('rule.form.name'),
  },
  {
    component: 'Input',
    fieldName: 'ruleKey',
    label: $t('rule.form.ruleKey'),
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
export const columns = computed((): VxeGridProps['columns'] => [
  { type: 'checkbox', width: 60 },
  {
    title: $t('rule.table.ruleId'),
    field: 'ruleId',
  },
  {
    title: $t('rule.table.name'),
    field: 'name',
  },
  {
    title: $t('rule.table.ruleKey'),
    field: 'ruleKey',
  },
  {
    title: $t('rule.table.type'),
    field: 'type',
    slots: {
      default: ({ row }) => {
        return renderDict(row.type, 'rule_type');
      },
    },
  },
  {
    title: $t('rule.table.content'),
    field: 'content',
  },
  {
    title: $t('rule.table.remark'),
    field: 'remark',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: $t('pages.common.action'),
    width: 180,
  },
]);

export const modalSchema: FormSchemaGetter = () => [
  {
    label: $t('rule.form.ruleId'),
    fieldName: 'ruleId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: $t('rule.form.name'),
    fieldName: 'name',
    component: 'Input',
    rules: 'required',
  },
  {
    label: $t('rule.form.ruleKey'),
    fieldName: 'ruleKey',
    component: 'Input',
    rules: 'required',
  },
  {
    label: $t('rule.form.type'),
    fieldName: 'type',
    component: 'Select',
    componentProps: {
      options: getDictOptions('rule_type'),
    },
    rules: 'selectRequired',
    defaultValue: '1',
  },
  {
    label: $t('rule.form.content'),
    fieldName: 'content',
    component: 'RichTextarea',
    componentProps: {},
    dependencies: {
      if: (value: Partial<Record<string, any>>) => {
        return value.type === '1'; // 富文本类型
      },
      triggerFields: ['type'],
    },
  },
  {
    label: $t('rule.form.content'),
    fieldName: 'content',
    component: 'Textarea',
    dependencies: {
      if: (value: Partial<Record<string, any>>) => {
        return value.type === '2'; // 链接或输入框类型
      },
      triggerFields: ['type'],
    },
  },
  {
    label: $t('rule.form.content'),
    fieldName: 'content',
    component: 'Input',
    dependencies: {
      if: (value: Partial<Record<string, any>>) => {
        return value.type === '3'; // 链接或输入框类型
      },
      triggerFields: ['type'],
    },
  },
  {
    label: $t('rule.form.remark'),
    fieldName: 'remark',
    component: 'Input',
  },
];
