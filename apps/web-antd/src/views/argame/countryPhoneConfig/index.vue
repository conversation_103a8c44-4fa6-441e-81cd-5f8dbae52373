<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { CountryPhoneConfigForm } from '#/api/argame/countryPhoneConfig/model';

import { Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { getVxePopupContainer } from '@vben/utils';

import { Popconfirm, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  countryPhoneConfigList,
  countryPhoneConfigRemove,
} from '#/api/argame/countryPhoneConfig';

import countryPhoneConfigModal from './countryPhoneConfig-modal.vue';
import { columns, querySchema } from './data';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  // 处理区间选择器RangePicker时间格式 将一个字段映射为两个字段 搜索/导出会用到
  // 不需要直接删除
  // fieldMappingTime: [
  //  [
  //    'createTime',
  //    ['params[beginTime]', 'params[endTime]'],
  //    ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
  //  ],
  // ],
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns: columns.value,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await countryPhoneConfigList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'countryPhoneConfigId',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'argame-countryPhoneConfig-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  gridOptions,
});

const [CountryPhoneConfigModal, modalApi] = useVbenModal({
  connectedComponent: countryPhoneConfigModal,
});

function handleAdd() {
  modalApi.setData({});
  modalApi.open();
}

async function handleEdit(row: Required<CountryPhoneConfigForm>) {
  modalApi.setData({ id: row.countryPhoneConfigId });
  modalApi.open();
}

async function handleDelete(row: Required<CountryPhoneConfigForm>) {
  await countryPhoneConfigRemove(row.countryPhoneConfigId);
  await tableApi.query();
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="区号列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            type="primary"
            v-access:code="['argame:countryPhoneConfig:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['argame:countryPhoneConfig:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </ghost-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <ghost-button
              danger
              v-access:code="['argame:countryPhoneConfig:remove']"
              @click.stop=""
            >
              {{ $t('pages.common.delete') }}
            </ghost-button>
          </Popconfirm>
        </Space>
      </template>
      <template #nationalFlag="{ row }">
        <Image
          v-if="row.nationalFlag"
          :src="row.nationalFlag"
          :height="80"
          style="border-radius: 4px"
          :preview="true"
        />
        <template v-else>-</template>
      </template>
    </BasicTable>
    <CountryPhoneConfigModal @reload="tableApi.query()" />
  </Page>
</template>
