import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { computed } from 'vue';

import { $t } from '@vben/locales';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns = computed((): VxeGridProps['columns'] => [
  { type: 'checkbox', width: 60 },
  {
    title: $t('countryPhoneConfig.table.country'),
    field: 'country',
    minWidth: 150,
  },
  {
    title: $t('countryPhoneConfig.table.countryCode'),
    field: 'countryCode',
    minWidth: 150,
  },
  {
    title: $t('countryPhoneConfig.table.nationalFlag'),
    field: 'nationalFlag',
    slots: { default: 'nationalFlag' },
    minWidth: 150,
  },
  {
    title: $t('countryPhoneConfig.table.areaCode'),
    field: 'areaCode',
    minWidth: 150,
  },
  {
    title: $t('countryPhoneConfig.table.mobileDigitLimit'),
    field: 'mobileDigitLimit',
    minWidth: 200,
  },
  {
    title: $t('countryPhoneConfig.table.status'),
    field: 'status',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.PHONE_STATUS 便于维护
        return renderDict(row.status, 'argame_sale_status');
      },
    },
    minWidth: 150,
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
]);

export const modalSchema: FormSchemaGetter = () => [
  {
    label: $t('countryPhoneConfig.form.id'),
    fieldName: 'countryPhoneConfigId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: $t('countryPhoneConfig.form.country'),
    fieldName: 'country',
    component: 'Input',
    rules: 'required',
  },
  {
    label: $t('countryPhoneConfig.form.countryCode'),
    fieldName: 'countryCode',
    component: 'Input',
    rules: 'required',
  },
  {
    label: $t('countryPhoneConfig.form.nationalFlag'),
    fieldName: 'nationalFlag',
    component: 'ImageUpload',
    rules: 'required',
    componentProps: {
      maxCount: 1, // 最大上传文件数 默认为1 为1会绑定为string而非string[]类型
      useUrl: true, // 使用url作为值而不是ossId
    },
  },
  {
    label: $t('countryPhoneConfig.form.areaCode'),
    fieldName: 'areaCode',
    component: 'Input',
    rules: 'required',
  },
  {
    label: $t('countryPhoneConfig.form.mobileDigitLimit'),
    fieldName: 'mobileDigitLimit',
    component: 'Input',
    rules: 'required',
    help: $t('countryPhoneConfig.form.mobileDigitLimitHelp'),
  },
  {
    label: $t('countryPhoneConfig.form.status'),
    fieldName: 'status',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.PHONE_STATUS 便于维护
      options: getDictOptions('argame_sale_status'),
    },
    defaultValue: '0',
    rules: 'selectRequired',
  },
];
