import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'markName',
    label: '行情名称',
  },
  {
    component: 'Input',
    fieldName: 'markKey',
    label: '行情KEY',
  },
  {
    component: 'Select',
    componentProps: {},
    fieldName: 'markCategoryId',
    label: '行情分类',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: 'ID',
    field: 'marketManagementId',
    minWidth: 180,
  },
  {
    title: '行情KEY',
    field: 'markKey',
    minWidth: 120,
  },
  {
    title: '行情分类',
    field: 'markCategoryName',
    minWidth: 120,
  },
  {
    title: '行情名称',
    field: 'markName',
  },
  {
    title: '行情图标',
    field: 'markImage',
    slots: { default: 'imageUrl' },
  },
  {
    title: '排序',
    field: 'sortOrder',
  },
  {
    title: '数据对象',
    field: 'userType',
  },
  {
    title: '是否热门',
    field: 'hotMark',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.ARGAME_SALE_STATUS 便于维护
        return renderDict(row.hotMark, 'argame_hot_mark');
      },
    },
  },
  {
    title: '状态',
    field: 'markStatus',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.ARGAME_SALE_STATUS 便于维护
        return renderDict(row.markStatus, 'argame_sale_status');
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '行情管理id',
    fieldName: 'marketManagementId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '行情分类',
    fieldName: 'markCategoryId',
    component: 'Select',
    rules: 'required',
  },
  {
    label: '行情图标',
    fieldName: 'markImage',
    component: 'ImageUpload',
    componentProps: {
      // accept: 'image/*', // 可选拓展名或者mime类型 ,拼接
      maxCount: 1, // 最大上传文件数 默认为1 为1会绑定为string而非string[]类型
    },
    rules: 'required',
  },
  {
    label: '行情名称',
    fieldName: 'markName',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '最低投注',
    fieldName: 'minAmount',
    component: 'InputNumber',
    rules: 'required',
  },
  {
    label: '最高投注',
    fieldName: 'maxAmount',
    component: 'InputNumber',
    rules: 'required',
  },
  {
    label: '交易手续费',
    fieldName: 'tradeFee',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '赔率',
    fieldName: 'tradeOdds',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '玩法时间',
    fieldName: 'playTimes',
    component: 'Custom',
    formItemClass: 'col-span-2',
    slot: 'playTimes',
  },
  {
    label: '快捷方式',
    fieldName: 'quickMethods',
    component: 'Custom',
    formItemClass: 'col-span-2',
    slot: 'quickMethods',
  },
  {
    label: '用户类型',
    fieldName: 'userType',
    component: 'CheckboxGroup',
    rules: 'selectRequired',
    componentProps: {
      options: getDictOptions('argame_user_type'),
      multiple: true,
    },
  },
  {
    label: '是否热门',
    fieldName: 'hotMark',
    component: 'Select',
    componentProps: {
      options: getDictOptions('argame_hot_mark'),
    },
    rules: 'selectRequired',
  },
  {
    label: '状态',
    fieldName: 'markStatus',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.ARGAME_SALE_STATUS 便于维护
      options: getDictOptions('argame_sale_status'),
    },
    rules: 'selectRequired',
  },
  {
    label: '备注',
    fieldName: 'remarks',
    component: 'Textarea',
  },
  {
    label: '排序',
    fieldName: 'sortOrder',
    component: 'InputNumber',
  },
];
