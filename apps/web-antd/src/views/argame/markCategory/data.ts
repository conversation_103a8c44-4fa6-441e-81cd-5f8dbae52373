import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '分类id',
    field: 'markCategoryId',
    visible: false,
  },
  {
    title: '分类名称',
    field: 'categoryName',
    minWidth: 150,
  },
  {
    title: '封面',
    field: 'coverImage',
    minWidth: 150,
    slots: { default: 'imageUrl' },
  },
  {
    title: '用户类型',
    field: 'userType',
    slots: {
      default: ({ row }) => {
        return renderDict(row.userType, 'argame_user_type');
      },
    },
    minWidth: 150,
  },
  {
    title: '状态',
    field: 'status',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.ARGAME_SALE_STATUS 便于维护
        return renderDict(row.status, 'argame_sale_status');
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '分类id',
    fieldName: 'markCategoryId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '分类名称',
    fieldName: 'categoryName',
    component: 'Input',
  },
  {
    label: '封面',
    fieldName: 'coverImage',
    component: 'ImageUpload',
    componentProps: {
      // accept: 'image/*', // 可选拓展名或者mime类型 ,拼接
      // maxCount: 1, // 最大上传文件数 默认为1 为1会绑定为string而非string[]类型
      useUrl: true, // 使用url作为值而不是ossId
    },
    rules: 'required',
  },
  {
    label: '用户类型',
    fieldName: 'userType',
    component: 'CheckboxGroup',
    rules: 'selectRequired',
    componentProps: {
      options: getDictOptions('argame_user_type'),
      multiple: true,
    },
  },
  {
    label: '状态',
    fieldName: 'status',
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.ARGAME_SALE_STATUS 便于维护
      options: getDictOptions('argame_sale_status'),
    },
  },
];
