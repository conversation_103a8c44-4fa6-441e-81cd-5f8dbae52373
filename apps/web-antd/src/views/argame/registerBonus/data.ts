import type { FormSchemaGetter, VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { computed } from 'vue';

import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

// 表格列配置
export const recordColumnsList = computed((): VxeGridProps['columns'] => [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'registerRecordId',
  },
  {
    title: '领取日期时间',
    field: 'receiveTime',
  },
  {
    title: '用户ID',
    field: 'userId',
  },
  {
    title: '用户名',
    field: 'userIdentity',
    slots: {
      default: ({ row }) => {
        return row.userVo?.username;
      },
    },
  },
  {
    title: '用户名',
    field: 'phone',
    slots: {
      default: ({ row }) => {
        return row.userVo?.username;
      },
    },
  },
  {
    title: '手机号',
    field: 'phone',
    solts: {
      default: ({ row }) => {
        if (row.userVo?.areaCode === null || row.userVo?.phone === null) {
          return '无';
        }
        return `${row.userVo?.areaCode.toString()}_${row.userVo?.phone}`;
      },
    },
  },
  {
    title: '金额',
    field: 'amount',
  },
]);

// 分组tab的表格列配置
export const columnsSetting = computed((): VxeGridProps['columns'] => [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'registerSettingId',
  },
  {
    title: '用户身份',
    field: 'userType',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.ARGAME_SALE_STATUS 便于维护
        return renderDict(row.status, 'argame_user_type');
      },
    },
  },
  {
    title: '赠金金额',
    field: 'amount',
  },
  {
    title: '领取方式',
    field: 'receiveType',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.ARGAME_SALE_STATUS 便于维护
        return renderDict(row.status, 'argame_receive_type');
      },
    },
  },
  {
    title: '活动状态',
    field: 'status',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.ARGAME_SALE_STATUS 便于维护
        return renderDict(row.status, 'argame_status');
      },
    },
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
]);

// 分组tab的查询表单配置
export const querySchemaRecord: VbenFormProps['schema'] = [
  {
    component: 'Input',
    fieldName: 'userId',
    label: 'UID',
  },
  {
    component: 'Input',
    fieldName: 'username',
    label: '用户名',
  },
  {
    component: 'Input',
    fieldName: 'phone',
    label: '手机号',
  },
];

export const modalSchema: FormSchemaGetter = () => [
  {
    label: '主键ID',
    fieldName: 'registerSettingId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '用户身份',
    fieldName: 'userType',
    component: 'Select',
    rules: 'required',
    componentProps: {
      options: getDictOptions('argame_user_type'),
    },
  },
  {
    label: '赠金金额',
    fieldName: 'amount',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '领取方式',
    fieldName: 'receiveType',
    component: 'Select',
    componentProps: {
      options: getDictOptions('argame_receive_type'),
    },
    rules: 'selectRequired',
  },
  {
    label: '活动状态',
    fieldName: 'status',
    component: 'Select',
    rules: 'selectRequired',
    componentProps: {
      options: getDictOptions('argame_status'),
    },
  },
];
