import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { computed } from 'vue';

// 加密货币
export const cryptocurrencyColumns = computed((): VxeGridProps['columns'] => [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'currencyExchangeId',
    visible: false,
  },
  {
    title: '名称',
    field: 'name',
  },
  {
    title: '图标',
    field: 'iconUrl',
    slots: { default: 'imageUrl' },
  },
  {
    title: '美元',
    field: 'USD',
    slots: {
      default: () => {
        return 'USD';
      },
    },
  },
  {
    title: '数量',
    field: 'fiatAmount',
    slots: {
      default: () => {
        return '1';
      },
    },
  },
  {
    title: '加密货币',
    field: 'cryptoCurrency',
  },
  {
    title: '加密货币数量',
    field: 'cryptoAmount',
  },
  {
    title: '最低充值金额',
    field: 'minDepositAmount',
  },
  {
    title: '最高充值金额',
    field: 'maxDepositAmount',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
]);

// 法定货币
export const columnsFranchise = computed((): VxeGridProps['columns'] => [
  { type: 'checkbox', width: 60 },
  {
    title: '主键ID',
    field: 'currencyExchangeId',
    visible: false,
  },
  {
    title: '名称',
    field: 'name',
  },
  {
    title: '图标',
    field: 'iconUrl',
    slots: { default: 'imageUrl' },
  },
  {
    title: '美元',
    field: 'USD',
    slots: {
      default: () => {
        return 'USD';
      },
    },
  },
  {
    title: '数量',
    field: 'USDNumber',
    slots: {
      default: () => {
        return '1';
      },
    },
  },
  {
    title: '法币代码',
    field: 'fiatCurrency',
  },
  {
    title: '法币数量',
    field: 'fiatAmount',
  },
  {
    title: '最低充值金额',
    field: 'minDepositAmount',
  },
  {
    title: '最高充值金额',
    field: 'maxDepositAmount',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
]);

export const modalSchema: FormSchemaGetter = (tabKey?: string) => [
  {
    label: '主键ID',
    fieldName: 'currencyExchangeId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '名称',
    fieldName: 'name',
    component: 'Input',
    rules: 'required',
  },
  {
    label: '图标URL',
    fieldName: 'iconUrl',
    component: 'ImageUpload',
    componentProps: {
      // accept: 'image/*', // 可选拓展名或者mime类型 ,拼接
      // maxCount: 1, // 最大上传文件数 默认为1 为1会绑定为string而非string[]类型
      useUrl: true, // 使用url作为值而不是ossId
    },
    rules: 'required',
  },
  {
    label: '美元',
    fieldName: 'USD',
    component: 'Input',
    rules: 'required',
    dependencies: {
      disabled: () => true,
      triggerFields: [''],
    },
    defaultValue: 'USD',
  },
  {
    label: '数量',
    fieldName: 'USDNumber',
    component: 'Input',
    rules: 'required',
    dependencies: {
      disabled: () => true,
      triggerFields: [''],
    },
    defaultValue: '1',
  },
  {
    label: '法币',
    fieldName: 'fiatCurrency',
    component: 'Input',
    rules: 'required',
    dependencies: {
      show: () => tabKey === 'setting',
      triggerFields: [''],
    },
  },
  {
    label: '法币数量',
    fieldName: 'fiatAmount',
    component: 'Input',
    rules: 'required',
    dependencies: {
      show: () => tabKey === 'setting',
      triggerFields: [''],
    },
  },
  {
    label: '加密货币',
    fieldName: 'cryptoCurrency',
    component: 'Input',
    rules: 'required',
    dependencies: {
      show: () => tabKey === 'list',
      triggerFields: [''],
    },
  },
  {
    label: '加密货币数量',
    fieldName: 'cryptoAmount',
    component: 'InputNumber',
    rules: 'required',
    dependencies: {
      show: () => tabKey === 'list',
      triggerFields: [''],
    },
  },
  {
    label: '最低充值金额',
    fieldName: 'minDepositAmount',
    component: 'InputNumber',
    rules: 'required',
  },
  {
    label: '最高充值金额',
    fieldName: 'maxDepositAmount',
    component: 'InputNumber',
    rules: 'required',
  },
];
