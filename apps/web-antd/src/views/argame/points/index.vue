<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type { PointsForm } from '#/api/argame/points/model';

import {computed, onMounted, ref} from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { getVxePopupContainer } from '@vben/utils';

import { Popconfirm, RadioButton, RadioGroup, Space } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { pointsList, pointsRemove } from '#/api/argame/points';
import { pointsRecordList } from '#/api/argame/pointsRecord';
import { $t } from '#/locales';
import addGroup from '#/views/argame/registerBonus/addSetting/index.vue';

import {
  pointsListColumns,
  pointsRecordColumns,
  querySchemaRecord,
} from './data';

const tabKey = ref('list');

const formOptions: VbenFormProps = {
  collapsed: false,
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  commonConfig: {
    labelWidth: 120,
    componentProps: {
      allowClear: true,
    },
  },
  showCollapseButton: true,
  submitOnChange: false,
  submitOnEnter: false,
};

const gridOptions: VxeGridProps = {
  columns: pointsRecordColumns.value,
  height: 800,
  // minHeight: 400,
  toolbarConfig: {
    custom: true,
    refresh: true,
    zoom: true,
  },
  columnConfig: {
    minWidth: 120,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const getApi = tabKey.value === 'list' ? pointsRecordList : pointsList;
        const params = {
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
          isAsc: 'desc',
          orderByColumn: 'createTime',
        };
        return await getApi(params);
      },
    },
  },
};

// 计算属性：根据tabKey动态生成表单配置
const computedFormOptions = computed(() => {
  return tabKey.value === 'list' ? formOptions : false;
});
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: computedFormOptions,
  gridOptions,
});

const [AddGroup, addGroupApi] = useVbenModal({
  connectedComponent: addGroup,
});

const handleAdd = () => {
  addGroupApi.setData({}).open();
};
const handleEdit = (row: any) => {
  addGroupApi.setData(row).open();
};

async function handleDelete(row: Required<PointsForm>) {
  await pointsRemove(row.pointId);
  await gridApi?.reload();
}

const radioChange = (e: any) => {
  const val = e.target.value;

  // 切换表格列
  gridApi.setGridOptions({
    ...gridOptions,
    columns:
      val === 'setting' ? pointsListColumns.value : pointsRecordColumns.value,
  });

  // 切换表单schema（仅在list模式下）
  if (val === 'list') {
    const schema = querySchemaRecord;
    gridApi.formApi?.setState({ schema });
  }

  gridApi?.formApi?.resetForm();
  gridApi?.reload();
};

const handleReload = () => {
  gridApi?.reload();
};

onMounted(() => {
  // 初始化时设置默认的表单schema（list模式）
  if (tabKey.value === 'list') {
    const schema = querySchemaRecord;
    gridApi.formApi?.setState({ schema });
  }
});
</script>

<template>
  <div class="flex h-full flex-col gap-3 p-5" style="padding-bottom: 12px">
    <RadioGroup
      v-model:value="tabKey"
      button-style="solid"
      @change="radioChange"
    >
      <RadioButton class="min-w-[100px] text-center" value="list">
        积分记录
      </RadioButton>
      <RadioButton class="min-w-[100px] text-center" value="setting">
        积分兑换配置
      </RadioButton>
    </RadioGroup>
    <div class="h-full overflow-y-hidden">
      <Grid class="h-[calc(100%-8px)]">
        <template #toolbar-tools>
          <Space>
            <a-button
              type="primary"
              v-access:code="['argame:registerBonusSetting:add']"
              @click="handleAdd"
              v-if="tabKey === 'setting'"
            >
              {{ $t('pages.common.add') }}
            </a-button>
          </Space>
        </template>
        <template #action="{ row }">
          <a-button
            type="link"
            :tooltip="false"
            size="small"
            @click="handleEdit(row)"
          >
            {{ $t('pages.common.edit') }}
          </a-button>
          <Popconfirm
            :get-popup-container="getVxePopupContainer"
            placement="left"
            title="确认删除？"
            @confirm="handleDelete(row)"
          >
            <a-button
              :tooltip="false"
              size="small"
              type="link"
              v-access:code="['argame:registerBonusSetting:remove']"
              @click.stop=""
            >
              {{ '删除' }}
            </a-button>
          </Popconfirm>
        </template>
      </Grid>
    </div>
    <AddGroup @reload="handleReload" />
  </div>
</template>
