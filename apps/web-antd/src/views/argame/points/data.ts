import type { FormSchemaGetter, VbenFormProps } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { computed } from 'vue';

import dayjs from 'dayjs';

import { renderDict } from '#/utils/render';

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const pointsListColumns = computed((): VxeGridProps['columns'] => [
  { type: 'checkbox', width: 60 },
  {
    title: '积分id',
    field: 'pointId',
    visible: false,
  },
  {
    title: '积分',
    field: 'userPoints',
  },
  {
    title: '金额',
    field: 'amount',
  },
  {
    title: '库存',
    field: 'inventory',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
]);

export const querySchemaRecord: VbenFormProps['schema'] = [
  {
    component: 'Input',
    fieldName: 'userId',
    label: 'UID',
  },
  {
    component: 'Input',
    fieldName: 'orderNo',
    label: '关联订单号',
  },
  {
    component: 'Input',
    fieldName: 'pointType',
    label: '类型',
  },
];

export const pointsRecordColumns = computed((): VxeGridProps['columns'] => [
  { type: 'checkbox', width: 60 },
  {
    title: '积分领取记录id',
    field: 'pointRecordId',
    visible: false,
  },
  {
    title: 'UID',
    field: 'userId',
  },
  {
    title: '关联订单号',
    field: 'orderNo',
  },
  {
    title: '积分',
    field: 'usePoints',
  },
  {
    title: '类型',
    field: 'pointType',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.USER_FLOW_SOURCE 便于维护
        return renderDict(row.pointType, 'argame_points_type');
      },
    },
  },
  {
    title: '发生时间',
    field: 'exchangeTime',
    slots: {
      default: ({ row }) => {
        if (!row.exchangeTime) return '--';
        // 将时间戳转换为年月日时分秒格式
        return dayjs(Number(row.exchangeTime)).format('YYYY-MM-DD HH:mm:ss');
      },
    },
  },
]);

export const pointsModalSchema: FormSchemaGetter = () => [
  {
    label: '积分id',
    fieldName: 'pointId',
    component: 'Input',
    dependencies: {
      show: () => false,
      triggerFields: [''],
    },
  },
  {
    label: '积分',
    fieldName: 'userPoints',
    component: 'InputNumber',
  },
  {
    label: '金额',
    fieldName: 'amount',
    component: 'InputNumber',
    componentProps: {
      min: 1,
    },
  },
  {
    label: '库存',
    fieldName: 'inventory',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
  },
];
